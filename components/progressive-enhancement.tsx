"use client";

import { useEffect, useState } from "react";

/**
 * Progressive Enhancement Hook
 *
 * Manages the loading of non-critical features after the initial UI is interactive
 * This ensures the fastest possible time to interactive while still providing
 * full functionality once the page has settled
 */
export const useProgressiveEnhancement = () => {
  const [enhancementsLoaded, setEnhancementsLoaded] = useState(false);
  const [interactionDetected, setInteractionDetected] = useState(false);

  // Load enhancements after initial render
  useEffect(() => {
    const loadEnhancements = () => {
      if (!enhancementsLoaded) {
        setEnhancementsLoaded(true);
        console.log("[ProgressiveEnhancement] Loading non-critical features");
      }
    };

    // Strategy 1: Load after a delay to ensure critical path is complete
    const delayTimer = setTimeout(loadEnhancements, 1000);

    // Strategy 2: Load on first user interaction
    const handleInteraction = () => {
      if (!interactionDetected) {
        setInteractionDetected(true);
        loadEnhancements();
      }
    };

    // Listen for various interaction events
    const events = [
      "mousedown",
      "mousemove",
      "keydown",
      "scroll",
      "touchstart",
    ];
    events.forEach((event) => {
      document.addEventListener(event, handleInteraction, {
        once: true,
        passive: true,
      });
    });

    // Strategy 3: Load when page is idle
    if ("requestIdleCallback" in window) {
      const idleCallback = (window as any).requestIdleCallback(
        () => {
          loadEnhancements();
        },
        { timeout: 2000 }
      );

      return () => {
        clearTimeout(delayTimer);
        events.forEach((event) => {
          document.removeEventListener(event, handleInteraction);
        });
        (window as any).cancelIdleCallback(idleCallback);
      };
    }

    return () => {
      clearTimeout(delayTimer);
      events.forEach((event) => {
        document.removeEventListener(event, handleInteraction);
      });
    };
  }, [enhancementsLoaded, interactionDetected]);

  return {
    enhancementsLoaded,
    interactionDetected,
  };
};

/**
 * Progressive Enhancement Component
 *
 * Wraps components that should be loaded progressively
 */
interface ProgressiveEnhancementProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  priority?: "high" | "medium" | "low";
}

export const ProgressiveEnhancement = ({
  children,
  fallback = null,
  priority = "medium",
}: ProgressiveEnhancementProps) => {
  const { enhancementsLoaded, interactionDetected } =
    useProgressiveEnhancement();

  // Determine when to show content based on priority
  const shouldShow = () => {
    switch (priority) {
      case "high":
        return interactionDetected || enhancementsLoaded;
      case "medium":
        return enhancementsLoaded;
      case "low":
        return enhancementsLoaded && interactionDetected;
      default:
        return enhancementsLoaded;
    }
  };

  if (shouldShow()) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
};

/**
 * Preload Critical Resources
 *
 * Preloads resources that will be needed soon but aren't critical for initial render
 */
export const preloadCriticalResources = () => {
  // Preload commonly used images
  const imagesToPreload = [
    "/mascot.svg",
    "/heart.svg",
    "/points.svg",
    "/unlimited.svg",
  ];

  imagesToPreload.forEach((src) => {
    const link = document.createElement("link");
    link.rel = "preload";
    link.as = "image";
    link.href = src;
    document.head.appendChild(link);
  });

  // Preload critical API endpoints
  const endpointsToPreload = ["/api/user-progress", "/api/courses"];

  endpointsToPreload.forEach((endpoint) => {
    fetch(endpoint, { method: "HEAD" }).catch(() => {
      // Silently fail - this is just a preload optimization
    });
  });
};

/**
 * Initialize Progressive Enhancement Hook
 *
 * Use this hook in your main layout or app component
 */
export const useInitializeProgressiveEnhancement = () => {
  useEffect(() => {
    // Preload resources after initial render
    const timer = setTimeout(() => {
      preloadCriticalResources();
    }, 500);

    return () => clearTimeout(timer);
  }, []);
};
