import { ReactNode } from "react";
import { headers } from "next/headers";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { ProgressiveSidebar } from "@/components/sidebar/sidebar-progressive";
import { MobileHeader } from "@/components/mobile-header";
import { getOnboardingStatus } from "@/lib/server-auth";

type Props = {
  children: ReactNode;
  params: {}; // Layouts receive a params object
};

export default async function MainLayout({ children }: Props) {
  // Server-side authentication check
  const { userId } = await auth();

  if (!userId) {
    // Redirect to sign-in if not authenticated
    redirect("/");
  }

  // Check if user is fully onboarded
  const isOnboarded = await getOnboardingStatus(userId);

  if (!isOnboarded) {
    // Redirect to onboarding if not fully set up
    redirect("/joinSchool");
  }

  // --- TWEAKED EMBEDDED DETECTION ---
  // Reliably checks for an iframe context using the standard `sec-fetch-dest` header.
  const headersList = headers();
  const isEmbedded = headersList.get("sec-fetch-dest") === "iframe";

  // If the page is embedded, return only the child component.
  // This bypasses the main layout, header, sidebar, and auth checks, giving
  // the embedded content the full viewport.
  if (isEmbedded) {
    return <div className="h-full w-full">{children}</div>;
  }

  console.log(
    "[MainLayout] Layout initialization with server-side auth complete."
  );

  // This is the default return for the full application experience.
  // Authentication and user validation now happens server-side
  return (
    <>
      <MobileHeader />
      <div className="flex h-screen">
        <ProgressiveSidebar />
        <main className="flex-1 h-full pt-[50px] lg:pt-0 overflow-auto">
          <div className="w-full h-full">{children}</div>
        </main>
      </div>
    </>
  );
}
