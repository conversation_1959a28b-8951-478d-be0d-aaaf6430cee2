import { ReactNode } from "react";

type Props = {
  children: ReactNode;
};

/**
 * Public Layout - For routes that don't require authentication
 * Used for sign-in, sign-up, joinSchool, etc.
 */
export default function PublicLayout({ children }: Props) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {children}
    </div>
  );
}
