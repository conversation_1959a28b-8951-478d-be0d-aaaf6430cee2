// virtualclassroomclient.tsx

"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import { getSocket } from "../../../lib/socketClient";
import type { Socket } from "socket.io-client";
import JitsiMeet from "./JitsiMeet";
import ChapterPage from "./ChapterPage";
import { Camera, Circle, Undo, Trash2, ArrowLeft } from "lucide-react";

import {
  layoutContainer,
  mainContentWrapper,
  panelClass,
} from "@/lib/responsiveUtilities";

import Draw from "./draw";

interface AnnotationData {
  userId: string;
  positions: { x: number; y: number }[];
}

interface Square {
  id: number;
  squareNumber: number;
  content: string;
  transliteration: string;
  audioUrl?: string;
  group: "basic" | "intermediate" | "advanced";
  overlayContent?: string;
}

interface ChapterData {
  id: number;
  title: string;
  squares: Square[];
  order?: number;
}

interface VirtualClassroomClientProps {
  isTeacher: boolean;
  userId: string;
  chapterData: ChapterData | null;
  canAnnotate?: boolean;
  annotations?: AnnotationData[];
  roomName?: string;
  jitsiSubject?: string;
}

export default function VirtualClassroomClient({
  isTeacher,
  userId,
  chapterData,
  canAnnotate = false,
  annotations = [],
  roomName,
  jitsiSubject,
}: VirtualClassroomClientProps) {
  const router = useRouter();

  const [localCanAnnotate, setLocalCanAnnotate] = useState(canAnnotate);
  const [localAnnotations, setLocalAnnotations] = useState(annotations);
  const [currentChapterData, setCurrentChapterData] = useState(chapterData);

  // Add loading state for chapter content only (not full page)
  const [isLoadingChapter, setIsLoadingChapter] = useState(false);

  useEffect(() => {
    setCurrentChapterData(chapterData);
  }, [chapterData]);

  // Enhanced fetchChapter with cache busting and loading state
  const fetchChapter = useCallback(async (order: number) => {
    try {
      setIsLoadingChapter(true);

      // Add timestamp for cache busting
      const timestamp = Date.now();
      const res = await fetch(`/api/chapters/${order}?t=${timestamp}`, {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });

      if (!res.ok) {
        throw new Error(`Error fetching chapter with order=${order}`);
      }

      const data = await res.json();
      console.log(
        `[fetchChapter] Fetched fresh data for order ${order}:`,
        data
      );

      return data;
    } catch (err) {
      console.error("fetchChapter error:", err);
      return null;
    } finally {
      setIsLoadingChapter(false);
    }
  }, []);

  const [mode, setMode] = useState<"lesson" | "draw">("lesson");
  const [studentCursorAllowed, setStudentCursorAllowed] = useState(false);
  const [teacherCursor, setTeacherCursor] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [studentCursor, setStudentCursor] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [highlightedSquareId, setHighlightedSquareId] = useState<number | null>(
    null
  );
  const [remoteHoveredSquareId, setRemoteHoveredSquareId] = useState<
    number | null
  >(null);
  const [remoteSelectedSquareId, setRemoteSelectedSquareId] = useState<
    number | null
  >(null);

  const [socket, setSocket] = useState<Socket | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [tool, setTool] = useState<"pen" | "eraser">("pen");
  const [color, setColor] = useState("#000000");
  const [lineWidth, setLineWidth] = useState(3);
  const [showTooltip, setShowTooltip] = useState<string | null>(null);

  const colors = [
    "#000000",
    "#FF3B30",
    "#FF9500",
    "#FFCC00",
    "#34C759",
    "#007AFF",
    "#AF52DE",
    "#FF2D55",
  ];

  const drawRef = useRef<any>(null);

  const derivedSubject = jitsiSubject
    ? jitsiSubject
    : currentChapterData?.title || "Lesson Session";

  const toggleStudentCursorAllowed = () => {
    const newVal = !studentCursorAllowed;
    setStudentCursorAllowed(newVal);
    if (!newVal) {
      setStudentCursor(null);
    }
    if (socket?.connected) {
      socket.emit("student-cursor-allowed", { allowed: newVal });
    }
  };

  const handleSetMode = (newMode: "lesson" | "draw") => {
    setMode(newMode);
    if (isTeacher && socket?.connected) {
      socket.emit("mode-changed", { mode: newMode });
    }
  };

  const clearCanvas = () => {
    if (drawRef.current && drawRef.current.clearCanvas) {
      drawRef.current.clearCanvas();
    }
  };

  const handleTooltipEnter = (tooltipText: string) => {
    setShowTooltip(tooltipText);
  };
  const handleTooltipLeave = () => {
    setShowTooltip(null);
  };

  // Initialize socket connection for virtual classroom features
  useEffect(() => {
    const initSocket = async () => {
      try {
        const socketInstance = await getSocket("virtual-classroom");
        setSocket(socketInstance);
      } catch (error) {
        console.error("[VirtualClassroom] Error initializing socket:", error);
      }
    };
    initSocket();

    // Cleanup: unregister feature when component unmounts
    return () => {
      import("../../../lib/socketClient").then(
        ({ unregisterSocketFeature }) => {
          unregisterSocketFeature("virtual-classroom");
        }
      );
    };
  }, []);

  useEffect(() => {
    if (!socket) return;
    if (socket.connected) {
      const finalId =
        userId && userId.trim() !== "" ? userId.trim() : "student_demo_user";
      console.log(`[Client] Emitting "register-role" with userId=${finalId}`);
      socket.emit("register-role", {
        userId: finalId,
        role: isTeacher ? "teacher" : "student",
      });
    } else {
      socket.on("connect", () => {
        const finalId =
          userId && userId.trim() !== "" ? userId.trim() : "student_demo_user";
        console.log(
          `[Client] Socket connected. Now emitting "register-role" with userId=${finalId}`
        );
        socket.emit("register-role", {
          userId: finalId,
          role: isTeacher ? "teacher" : "student",
        });
      });
    }

    const handleAnnotatorsUpdated = (data: any) => {
      if (Array.isArray(data.annotators) && data.annotators.includes(userId)) {
        setLocalCanAnnotate(true);
      }
    };

    const handleAnnotationUpdate = (data: {
      userId: string;
      stroke: { x: number; y: number }[];
    }) => {
      setLocalAnnotations((prev) => [
        ...prev,
        { userId: data.userId, positions: data.stroke },
      ]);
    };

    const handleClearAnnotations = (data: {
      all?: boolean;
      userId?: string;
    }) => {
      if (data.all) {
        setLocalAnnotations([]);
      } else if (data.userId) {
        setLocalAnnotations((prev) =>
          prev.filter((ann) => ann.userId !== data.userId)
        );
      }
    };

    const handleCursorUpdate = (data: {
      userId: string;
      xRatio: number;
      yRatio: number;
    }) => {
      if (!containerRef.current) return;
      const rect = containerRef.current.getBoundingClientRect();
      const localX = rect.left + data.xRatio * rect.width;
      const localY = rect.top + data.yRatio * rect.height;
      if (data.userId !== userId) {
        if (isTeacher) {
          setStudentCursor({ x: localX, y: localY });
        } else {
          setTeacherCursor({ x: localX, y: localY });
        }
      }
    };

    const handleHighlightUpdate = (data: {
      userId: string;
      squareId: number;
    }) => {
      setHighlightedSquareId(data.squareId);
    };

    const handleSquareHovered = (data: {
      userId: string;
      squareId: number | null;
    }) => {
      if (data.userId !== userId) {
        setRemoteHoveredSquareId(data.squareId);
      }
    };

    const handleSquareSelected = (data: {
      userId: string;
      squareId: number | null;
    }) => {
      if (data.userId !== userId) {
        setRemoteSelectedSquareId(data.squareId);
      }
    };

    // Enhanced chapter changed handler
    const handleChapterChanged = async (data: { newChapter: number }) => {
      console.log("[Client] Chapter changed to:", data.newChapter);

      // Set loading state for chapter content only
      setIsLoadingChapter(true);

      // Reset UI state immediately
      setHighlightedSquareId(null);
      setRemoteHoveredSquareId(null);
      setRemoteSelectedSquareId(null);

      // Fetch fresh chapter data
      const fetchedChapter = await fetchChapter(data.newChapter);
      if (fetchedChapter) {
        console.log("[Client] Setting fresh chapter data:", fetchedChapter);
        setCurrentChapterData(fetchedChapter);
      } else {
        console.warn("No data found for chapter:", data.newChapter);
      }

      // Clear loading state
      setIsLoadingChapter(false);
    };

    const handleStudentCursorAllowed = (data: { allowed: boolean }) => {
      setStudentCursorAllowed(data.allowed);
    };

    const handleModeChanged = (data: { mode: "lesson" | "draw" }) => {
      setMode(data.mode);
    };

    socket.on("annotators-updated", handleAnnotatorsUpdated);
    socket.on("annotation-update", handleAnnotationUpdate);
    socket.on("clear-annotations", handleClearAnnotations);
    socket.on("cursor-update", handleCursorUpdate);
    socket.on("highlight-update", handleHighlightUpdate);
    socket.on("square-hovered", handleSquareHovered);
    socket.on("square-selected", handleSquareSelected);
    socket.on("chapter-changed", handleChapterChanged);
    socket.on("student-cursor-allowed", handleStudentCursorAllowed);
    socket.on("mode-changed", handleModeChanged);

    return () => {
      socket.off("connect");
      socket.off("annotators-updated", handleAnnotatorsUpdated);
      socket.off("annotation-update", handleAnnotationUpdate);
      socket.off("clear-annotations", handleClearAnnotations);
      socket.off("cursor-update", handleCursorUpdate);
      socket.off("highlight-update", handleHighlightUpdate);
      socket.off("square-hovered", handleSquareHovered);
      socket.off("square-selected", handleSquareSelected);
      socket.off("chapter-changed", handleChapterChanged);
      socket.off("student-cursor-allowed", handleStudentCursorAllowed);
      socket.off("mode-changed", handleModeChanged);
    };
  }, [socket, userId, isTeacher, router, fetchChapter]);

  // TEACHER's Mouse Movement
  useEffect(() => {
    if (!isTeacher || !socket?.connected) return;
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      const rect = containerRef.current.getBoundingClientRect();
      const xInContainer = e.clientX - rect.left;
      const yInContainer = e.clientY - rect.top;
      const xRatio = xInContainer / rect.width;
      const yRatio = yInContainer / rect.height;
      socket.emit("cursor-update", { userId, xRatio, yRatio });
    };
    window.addEventListener("mousemove", handleMouseMove);
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, [isTeacher, userId, socket]);

  // STUDENT's Mouse Movement
  useEffect(() => {
    if (isTeacher || !studentCursorAllowed || !socket?.connected) return;
    const handleStudentMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      const rect = containerRef.current.getBoundingClientRect();
      const xInContainer = e.clientX - rect.left;
      const yInContainer = e.clientY - rect.top;
      const xRatio = xInContainer / rect.width;
      const yRatio = yInContainer / rect.height;
      socket.emit("cursor-update", { userId, xRatio, yRatio });
    };
    window.addEventListener("mousemove", handleStudentMouseMove);
    return () => {
      window.removeEventListener("mousemove", handleStudentMouseMove);
    };
  }, [isTeacher, userId, studentCursorAllowed, socket]);

  // Enhanced chapter change callback with better state management
  const onChapterChanged = useCallback(
    async (newChapter: number) => {
      console.log(`[onChapterChanged] Changing to chapter ${newChapter}`);

      // Set loading state for chapter content only
      setIsLoadingChapter(true);

      // Reset UI state immediately
      setHighlightedSquareId(null);
      setRemoteHoveredSquareId(null);
      setRemoteSelectedSquareId(null);

      // Emit chapter change if teacher
      if (isTeacher && socket?.connected) {
        socket.emit("chapter-changed", { newChapter });
      }

      // Fetch fresh data
      const fetchedChapter = await fetchChapter(newChapter);
      if (fetchedChapter) {
        console.log(
          `[onChapterChanged] Setting fresh data for chapter ${newChapter}:`,
          fetchedChapter
        );
        setCurrentChapterData(fetchedChapter);
      } else {
        console.warn("No data found for chapter:", newChapter);
      }

      // Clear loading state
      setIsLoadingChapter(false);
    },
    [isTeacher, socket, fetchChapter]
  );

  // Square highlight callbacks
  const handleSquareHighlight = useCallback(
    (squareId: number) => {
      if (isTeacher && socket?.connected) {
        socket.emit("highlight-update", { userId, squareId });
      }
    },
    [isTeacher, userId, socket]
  );

  const onSquareHover = useCallback(
    (squareId: number | null) => {
      if (isTeacher && socket?.connected) {
        socket.emit("square-hovered", { userId, squareId });
      }
    },
    [isTeacher, userId, socket]
  );

  const onSquareSelected = useCallback(
    (squareId: number | null) => {
      if (isTeacher && socket?.connected) {
        socket.emit("square-selected", { userId, squareId });
      }
    },
    [isTeacher, userId, socket]
  );

  // Show loading state while fetching - REMOVED FULL PAGE LOADING
  // This was causing the entire component to unmount and remount

  // Render
  return (
    <div
      className={`${layoutContainer} h-screen w-screen flex flex-col text-sm`} // Removed bg-gray-50, added text-sm from your example
      style={{
        position: "relative", // Keep existing style
        backgroundColor: "#ffffff",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
        backgroundRepeat: "repeat",
      }}
      ref={containerRef}
    >
      {/* Enhanced Header */}
      <header className="bg-white border-b border-gray-200 h-16 flex-shrink-0">
        <div className="h-full px-6 flex justify-between items-center">
          {/* Left Section: Back Button and Logo */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.push("/dashboard")}
              className="flex items-center justify-center w-9 h-9 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-300"
              title="Return to Dashboard"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <div className="h-8 w-px bg-gray-200"></div>
            <div className="flex items-center">
              <h1 className="text-2xl font-semibold text-neutral-800 tracking-wide">
                Iqra.
              </h1>
            </div>
          </div>

          {mode === "draw" && (
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-3 relative">
                {/* Pencil */}
                <button
                  onClick={() => {
                    setTool("pen");
                    setColor("#000000");
                    setLineWidth(3);
                  }}
                  className="relative w-10 h-10 overflow-hidden group"
                  onMouseEnter={() => handleTooltipEnter("Pencil")}
                  onMouseLeave={handleTooltipLeave}
                ></button>

                {/* Yellow Highlighter */}
                <button
                  onClick={() => {
                    setTool("pen");
                    setColor("#FFCC00");
                    setLineWidth(10);
                  }}
                  className="relative w-10 h-10 overflow-hidden group"
                  onMouseEnter={() => handleTooltipEnter("Yellow Highlighter")}
                  onMouseLeave={handleTooltipLeave}
                ></button>

                {/* Blue Highlighter */}
                <button
                  onClick={() => {
                    setTool("pen");
                    setColor("#007AFF");
                    setLineWidth(10);
                  }}
                  className="relative w-10 h-10 overflow-hidden group"
                  onMouseEnter={() => handleTooltipEnter("Blue Highlighter")}
                  onMouseLeave={handleTooltipLeave}
                ></button>

                {/* Eraser */}
                <button
                  onClick={() => {
                    setTool("eraser");
                  }}
                  className="relative w-10 h-10 overflow-hidden group"
                  onMouseEnter={() => handleTooltipEnter("Eraser")}
                  onMouseLeave={handleTooltipLeave}
                ></button>
              </div>

              {/* Line Width Slider */}
              <div className="flex items-center space-x-2 group">
                <Circle
                  className="w-5 h-5 text-gray-500 group-hover:text-gray-700 transition-colors"
                  style={{ strokeWidth: Math.max(1, lineWidth / 5) }}
                />
                <div className="relative">
                  <input
                    type="range"
                    min="1"
                    max="20"
                    value={lineWidth}
                    onChange={(e) => setLineWidth(parseInt(e.target.value))}
                    className="w-28 h-1.5 bg-gray-200 rounded-lg appearance-none cursor-pointer
                      [&::-webkit-slider-thumb]:appearance-none
                      [&::-webkit-slider-thumb]:w-4
                      [&::-webkit-slider-thumb]:h-4
                      [&::-webkit-slider-thumb]:rounded-full
                      [&::-webkit-slider-thumb]:bg-gray-700
                      [&::-webkit-slider-thumb]:hover:bg-gray-800
                      [&::-webkit-slider-thumb]:transition-colors
                      hover:[&::-webkit-slider-thumb]:scale-110
                      [&::-webkit-slider-thumb]:shadow-sm"
                    onMouseEnter={() => handleTooltipEnter("Adjust line width")}
                    onMouseLeave={handleTooltipLeave}
                  />
                  <div className="absolute -bottom-5 left-1/2 transform -translate-x-1/2 text-xs text-gray-500">
                    {lineWidth}px
                  </div>
                </div>
              </div>

              {/* Undo & Clear */}
              <div className="flex items-center space-x-1">
                <button
                  className="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg
                    hover:bg-gray-200 transition-all duration-200 flex items-center space-x-1 group"
                  onClick={() => {
                    // Potential undo logic (not implemented)
                  }}
                  onMouseEnter={() => handleTooltipEnter("Undo last action")}
                  onMouseLeave={handleTooltipLeave}
                >
                  <Undo className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
                  <span>Undo</span>
                </button>

                <button
                  className="px-3 py-2 text-sm font-medium text-white bg-gray-800 rounded-lg
                    hover:bg-gray-700 transition-all duration-200 flex items-center space-x-1 group
                    hover:shadow-md active:transform active:scale-95"
                  onClick={clearCanvas}
                  onMouseEnter={() => handleTooltipEnter("Clear canvas")}
                  onMouseLeave={handleTooltipLeave}
                >
                  <Trash2 className="w-4 h-4 group-hover:rotate-12 transition-transform" />
                  <span>Clear</span>
                </button>
              </div>
            </div>
          )}

          {/* Right Section: Mode Controls */}
          <div className="flex items-center gap-4">
            {isTeacher && (
              <div className="flex items-center gap-3 p-2 bg-white rounded-full shadow-sm border border-neutral-200">
                {/* Lesson vs Draw */}
                <div className="flex gap-1 bg-neutral-100 rounded-lg">
                  <button
                    onClick={() => handleSetMode("lesson")}
                    className={`px-3 py-1.5 rounded-full font-medium text-sm transition-all duration-200
                      ${
                        mode === "lesson"
                          ? "bg-neutral-900 text-white shadow-sm"
                          : "bg-transparent text-neutral-600 hover:bg-neutral-200"
                      }`}
                  >
                    Lesson
                  </button>
                  <button
                    onClick={() => handleSetMode("draw")}
                    className={`px-3 py-1.5 rounded-full font-medium text-sm transition-all duration-200
                      ${
                        mode === "draw"
                          ? "bg-neutral-900 text-white shadow-sm"
                          : "bg-transparent text-neutral-600 hover:bg-neutral-200"
                      }`}
                  >
                    Draw
                  </button>
                </div>

                {/* Student Cursor Access */}
                <div className="flex items-center space-x-2">
                  <div className="relative flex items-center">
                    <input
                      id="studentCursorCheckbox"
                      type="checkbox"
                      checked={studentCursorAllowed}
                      onChange={toggleStudentCursorAllowed}
                      className="peer sr-only"
                    />
                    <div
                      onClick={toggleStudentCursorAllowed}
                      className={`w-9 h-5 rounded-full bg-gray-300 peer-checked:bg-neutral-900 flex items-center p-0.5 cursor-pointer transition-colors duration-200`}
                    >
                      <span
                        className={`h-4 w-4 rounded-full bg-white transform transition-transform duration-200 ${
                          studentCursorAllowed ? "translate-x-4" : ""
                        }`}
                      ></span>
                    </div>
                    <label
                      htmlFor="studentCursorCheckbox"
                      className="ml-2 text-sm font-medium text-neutral-700 select-none cursor-pointer"
                    >
                      Cursor Access
                    </label>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Tooltip from the newly moved toolbar (centered on screen) */}
      {showTooltip && (
        <div
          className="fixed px-2 py-1 text-xs text-white bg-gray-800 rounded shadow-lg
          pointer-events-none transform -translate-x-1/2 transition-opacity duration-200"
          style={{
            left: "50%",
            bottom: "20px",
            opacity: showTooltip ? 1 : 0,
          }}
        >
          {showTooltip}
        </div>
      )}

      {/* Main layout */}
      <div
        className={`${mainContentWrapper} flex-1 p-4 gap-4 flex flex-col md:flex-row overflow-hidden`}
      >
        {/* Left: Video Call */}
        <div
          className={`${panelClass} w-full md:w-1/3 bg-white rounded-2xl shadow-sm border border-gray-200 p-4 flex flex-col`}
        >
          <div className="flex items-center mb-3">
            <Camera className="h-5 w-5 text-gray-800 mr-2" />
            <h2 className="text-sm font-medium text-gray-900">
              Live Session
              {currentChapterData?.title && (
                <span className="ml-2 text-xs text-gray-500">
                  ({currentChapterData.title})
                </span>
              )}
            </h2>
          </div>
          <div className="flex-1 min-h-0">
            <JitsiMeet
              roomName={roomName || "NoRoomName"}
              userName={userId}
              subject={derivedSubject}
            />
          </div>
        </div>

        {/* Right: Chapter Content or Draw */}
        <div className="relative w-full md:w-2/3 bg-white rounded-2xl shadow-sm border border-gray-200 p-4 flex flex-col min-h-0">
          {mode === "lesson" ? (
            isLoadingChapter ? (
              <div className="flex flex-col items-center justify-center flex-1 text-gray-500">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
                <p className="text-sm text-center">Loading chapter...</p>
              </div>
            ) : currentChapterData ? (
              <ChapterPage
                // Simple key that only changes when chapter ID changes
                key={`chapter-${currentChapterData.id}`}
                title={currentChapterData.title}
                squares={currentChapterData.squares}
                order={currentChapterData.order}
                onSquareHighlighted={handleSquareHighlight}
                highlightedSquareId={highlightedSquareId}
                isTeacher={isTeacher}
                hoveredSquareId={remoteHoveredSquareId}
                selectedSquareId={remoteSelectedSquareId}
                onSquareHover={onSquareHover}
                onSquareSelected={onSquareSelected}
                onChapterChange={onChapterChanged}
              />
            ) : (
              <div className="flex flex-col items-center justify-center flex-1 text-gray-500">
                <p className="text-sm text-center">No chapter data available</p>
              </div>
            )
          ) : (
            <Draw
              ref={drawRef}
              canDraw={isTeacher ? true : studentCursorAllowed}
              userId={
                userId && userId.trim() !== ""
                  ? userId.trim()
                  : "student_demo_user"
              }
              externalColor={color}
              externalLineWidth={lineWidth}
              externalTool={tool}
            />
          )}
        </div>
      </div>

      {/* Teacher sees student's cursor if allowed */}
      {isTeacher && studentCursor && (
        <div
          style={{
            position: "absolute",
            left: studentCursor.x,
            top: studentCursor.y,
            pointerEvents: "none",
            transform: "translate(-50%, -50%)",
            zIndex: 9999,
          }}
        >
          <div
            style={{
              width: 20,
              height: 20,
              borderRadius: "50%",
              backgroundColor: "blue",
              opacity: 0.7,
            }}
          />
        </div>
      )}

      {/* Student sees teacher's cursor */}
      {!isTeacher && teacherCursor && (
        <div
          style={{
            position: "absolute",
            left: teacherCursor.x,
            top: teacherCursor.y,
            pointerEvents: "none",
            transform: "translate(-50%, -50%)",
            zIndex: 9999,
          }}
        >
          <div
            style={{
              width: 20,
              height: 20,
              borderRadius: "50%",
              backgroundColor: "red",
              opacity: 0.7,
            }}
          />
        </div>
      )}
    </div>
  );
}
