// Quick database connection test
const postgres = require('postgres');

async function testConnection() {
  console.log('Testing database connection...');
  
  const DATABASE_URL = process.env.DATABASE_URL;
  console.log('DATABASE_URL exists:', !!DATABASE_URL);
  console.log('DATABASE_URL preview:', DATABASE_URL ? DATABASE_URL.substring(0, 20) + '...' : 'undefined');
  
  if (!DATABASE_URL) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return;
  }
  
  try {
    const sql = postgres(DATABASE_URL, {
      ssl: "require",
      max: 1,
      connect_timeout: 10,
    });
    
    console.log('Attempting to connect...');
    const result = await sql`SELECT 1 as test`;
    console.log('✅ Database connection successful!', result);
    
    // Test if users table exists
    try {
      const userCount = await sql`SELECT COUNT(*) as count FROM users`;
      console.log('✅ Users table accessible, count:', userCount[0].count);
    } catch (error) {
      console.log('❌ Users table error:', error.message);
    }
    
    await sql.end();
    console.log('Connection closed.');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
  }
}

testConnection();
