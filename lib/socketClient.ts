// lib/socketClient.ts

"use client";

import type { Socket } from "socket.io-client";

// Adjust URL if your Socket.io server runs elsewhere
const SERVER_URL = typeof window !== "undefined" ? window.location.origin : "";

// --- Enhanced Lazy Socket Instance with Deferred Connection ---
// Socket is now created only when first requested, not on module import
let socket: Socket | null = null;
let isConnecting = false;
let eventListenersSetup = false;

// State for the single socket instance, used for logging and state tracking.
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;

// --- Deferred Connection Management ---
// Track which features require socket connections
type SocketFeature =
  | "messaging"
  | "virtual-classroom"
  | "ai-tutor"
  | "unread-counts"
  | "general";

// Track active features to determine if socket should stay connected
const activeFeatures = new Set<SocketFeature>();

// Connection priority - some features are more critical than others
const FEATURE_PRIORITY: Record<SocketFeature, number> = {
  "ai-tutor": 1, // Highest priority - real-time AI conversation
  "virtual-classroom": 2, // High priority - real-time collaboration
  messaging: 3, // Medium priority - chat functionality
  "unread-counts": 4, // Lower priority - can be polled
  general: 5, // Lowest priority - general features
};

// Defer connection until actually needed
let connectionDeferred = true;

// ================================================================== //
// ENHANCEMENTS: State for Queue Status & Connection Quality
// ================================================================== //

// --- Step 11: Queue Status State ---
// This object holds the live status of the server-side message queue.
// It is exported so components can read the current status directly.
export const queueStatus = {
  isQueued: false,
  message: "ok",
};

// --- Step 12: Connection Quality Monitoring State ---
// This object holds live metrics about the connection quality.
// It is exported so components can read the current status directly.
export const connectionQuality = {
  latency: -1, // in ms, -1 for unknown
  jitter: -1, // in ms, -1 for unknown
  reconnects: 0,
  lastUpdated: 0,
};

const latencySamples: number[] = [];
let pingInterval: NodeJS.Timeout | null = null;
const PING_INTERVAL_MS = 5000; // Ping every 5 seconds
const MAX_LATENCY_SAMPLES = 10;

// Create socket instance with optimized settings
async function createSocket(): Promise<Socket> {
  if (socket && socket.connected) {
    return socket;
  }

  if (isConnecting) {
    // Wait for existing connection attempt
    return new Promise((resolve) => {
      const checkConnection = () => {
        if (socket && socket.connected) {
          resolve(socket);
        } else {
          setTimeout(checkConnection, 100);
        }
      };
      checkConnection();
    });
  }

  isConnecting = true;

  try {
    // Dynamic import of socket.io-client
    const { io } = await import("socket.io-client");

    socket = io(SERVER_URL, {
      transports: ["websocket", "polling"],
      timeout: 20000,
      forceNew: false,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      reconnectionAttempts: 5,
    });

    // Setup event listeners only once
    if (!eventListenersSetup) {
      setupEventListeners(socket);
      eventListenersSetup = true;
    }

    return socket;
  } finally {
    isConnecting = false;
  }
}

// --- Event Listeners Setup Function ---
function setupEventListeners(socketInstance: Socket) {
  socketInstance.on("connect", () => {
    console.log("Socket connected with id:", socketInstance.id);
    reconnectAttempts = 0; // Reset counter on successful connection

    // Step 11 & 12: Reset status on new connection
    queueStatus.isQueued = false;
    queueStatus.message = "ok";
    connectionQuality.reconnects = 0;
    window.dispatchEvent(
      new CustomEvent("socket-queue-status-change", { detail: queueStatus })
    );
    startPingMonitoring(); // Start connection quality monitoring

    // Enhanced register with retry logic
    const userId = localStorage.getItem("userId") || "guest_user";
    const role = localStorage.getItem("userRole") || "student";
    const currentChapter = localStorage.getItem("currentChapter");

    socketInstance.emit("register", {
      userId,
      role,
      timestamp: Date.now(),
      ...(currentChapter && { currentChapter: parseInt(currentChapter) }),
    });

    console.log("[socketClient] Sent 'register' event:", {
      userId,
      role,
      currentChapter,
    });

    // Request current chapter sync on reconnect
    if (currentChapter) {
      socketInstance.emit("request-chapter-sync", {
        requestedChapter: parseInt(currentChapter),
      });
    }
  });

  socketInstance.on("disconnect", (reason) => {
    console.log("Socket disconnected:", reason);
    // Step 12: Stop monitoring on disconnect to prevent errors
    stopPingMonitoring();

    // Reconnection is handled automatically by the `reconnection: true` option.
    // We can add custom logic here if needed, for example, to show a UI indicator.
    if (reason === "io client disconnect") {
      console.log(
        "Client manually disconnected. Will not auto-reconnect until getSocket() or socket.connect() is called."
      );
    } else {
      console.log(
        "Disconnection was unexpected. Reconnection will be attempted automatically."
      );
    }
  });

  socketInstance.on("connect_error", (error) => {
    console.error("Socket connection error:", error.message);
    reconnectAttempts++;
    // Step 12: Update connection quality metric
    connectionQuality.reconnects = reconnectAttempts;
  });

  socketInstance.on("reconnect", (attemptNumber) => {
    console.log("Socket reconnected after", attemptNumber, "attempts");
    reconnectAttempts = 0;
    // Step 11: After reconnecting, the queue is likely flushed.
    queueStatus.isQueued = false;
    queueStatus.message = "flushed";
    window.dispatchEvent(
      new CustomEvent("socket-queue-status-change", { detail: queueStatus })
    );
  });

  socketInstance.on("reconnect_failed", () => {
    console.error(
      `Socket reconnection failed after ${MAX_RECONNECT_ATTEMPTS} attempts.`
    );
    // Step 12: Final state on failure
    stopPingMonitoring();
  });

  // ================================================================== //
  // ENHANCEMENTS: New Event Handlers for Queue & Quality
  // ================================================================== //

  // --- Step 11: Handler for OpenAI Reconnecting (Queue Status Indicator) ---
  // This event is sent from the server when it loses its connection to OpenAI
  // and starts queueing messages.
  socketInstance.on(
    "openai-reconnecting",
    (data: { attempt: number; delay: number }) => {
      console.warn(
        `[socketClient] Server is queueing messages. Reconnecting to OpenAI (Attempt ${data.attempt})`
      );
      queueStatus.isQueued = true;
      queueStatus.message = `reconnecting (attempt ${data.attempt})`;
      // Dispatch a global event for components to listen to
      window.dispatchEvent(
        new CustomEvent("socket-queue-status-change", { detail: queueStatus })
      );
    }
  );

  // --- Step 12: Handler for Server Ping Response ---
  // Listens for the 'pong' from the server to calculate latency.
  socketInstance.on("server-pong", (data: { startTime: number }) => {
    const latency = Date.now() - data.startTime;
    latencySamples.push(latency);
    if (latencySamples.length > MAX_LATENCY_SAMPLES) {
      latencySamples.shift(); // Keep only the last N samples
    }

    // Update connection quality metrics
    connectionQuality.latency = latency;
    connectionQuality.jitter = calculateStandardDeviation(latencySamples);
    connectionQuality.lastUpdated = Date.now();

    // Dispatch a global event for components to listen to
    window.dispatchEvent(
      new CustomEvent("socket-quality-update", { detail: connectionQuality })
    );
  });

  // -----------------------------------------------
  // Keep existing annotation logic
  // -----------------------------------------------
  socketInstance.on("cursor-update", (data) => {
    // Example: console.log("Received cursor-update:", data);
  });

  socketInstance.on("highlight-update", (data) => {
    // Example: console.log("Received highlight-update:", data);
  });

  // Enhanced chapter-changed handler with validation
  socketInstance.on("chapter-changed", (data) => {
    console.log("[socketClient] 'chapter-changed' received:", data);

    if (data && typeof data.newChapter === "number") {
      // Store current chapter for reconnection sync
      localStorage.setItem("currentChapter", data.newChapter.toString());

      // Emit chapter sync confirmation
      socketInstance.emit("chapter-sync-confirmed", {
        chapter: data.newChapter,
        timestamp: Date.now(),
      });
    }
  });

  // Handle chapter sync responses
  socketInstance.on("chapter-sync-response", (data) => {
    console.log("[socketClient] 'chapter-sync-response' received:", data);

    if (data && data.currentChapter) {
      localStorage.setItem("currentChapter", data.currentChapter.toString());

      // Trigger a custom event that components can listen to
      window.dispatchEvent(
        new CustomEvent("socket-chapter-sync", {
          detail: { chapter: data.currentChapter },
        })
      );
    }
  });

  // ADDED: meeting-started
  socketInstance.on("meeting-started", (data) => {
    console.log("[socketClient] 'meeting-started' received:", data);
    // Could handle logic here or let your component handle it
  });

  // NEW: Listen for new-message events (optional, depending on your client logic)
  socketInstance.on("new-message", (data) => {
    console.log("[socketClient] 'new-message' received:", data);
  });

  // NEW: Listen for unread-update events to update unread count in real time
  socketInstance.on("unread-update", (data) => {
    console.log("[socketClient] 'unread-update' received:", data);
    // Handle the unread count update, e.g., update global state or notify components
  });

  // Step 1: Add a specific handler for the OpenAI conversation closure.
  // This event is proxied from the server when the OpenAI Realtime API session ends.
  // By handling it here without disconnecting, we keep the main client-server
  // socket.io connection alive for subsequent interactions.
  socketInstance.on("openai-conversation-closed", (data) => {
    console.log(
      "[socketClient] 'openai-conversation-closed' received:",
      (data && data.reason) || "Conversation ended."
    );
    // CRITICAL: We do NOT call disconnectSocket() here.
    // This allows the primary socket.io connection to persist even
    // after a specific AI conversation turn has concluded.
  });

  // Force-refresh event for critical updates
  socketInstance.on("force-refresh", (data) => {
    console.log("[socketClient] 'force-refresh' received:", data);

    if (
      data.reason === "database-reset" ||
      data.reason === "chapter-data-updated"
    ) {
      // Clear any cached data
      localStorage.removeItem("currentChapter");

      // Trigger page refresh or reload chapter data
      window.dispatchEvent(
        new CustomEvent("socket-force-refresh", {
          detail: data,
        })
      );
    }
  });
}

// --- Deferred Connection Management Functions ---

/**
 * Register a feature that needs socket connection
 */
export function registerSocketFeature(feature: SocketFeature): void {
  activeFeatures.add(feature);
  console.log(
    `[socketClient] Feature '${feature}' registered. Active features:`,
    Array.from(activeFeatures)
  );
}

/**
 * Unregister a feature that no longer needs socket connection
 */
export function unregisterSocketFeature(feature: SocketFeature): void {
  activeFeatures.delete(feature);
  console.log(
    `[socketClient] Feature '${feature}' unregistered. Active features:`,
    Array.from(activeFeatures)
  );

  // If no features are active, we could potentially disconnect
  // But we'll keep the connection alive for now to avoid reconnection overhead
}

/**
 * Check if socket connection is needed based on active features
 */
function isSocketNeeded(): boolean {
  return activeFeatures.size > 0;
}

/**
 * Get the highest priority active feature
 */
function getHighestPriorityFeature(): SocketFeature | null {
  if (activeFeatures.size === 0) return null;

  let highestPriority = Infinity;
  let priorityFeature: SocketFeature | null = null;

  for (const feature of activeFeatures) {
    const priority = FEATURE_PRIORITY[feature];
    if (priority < highestPriority) {
      highestPriority = priority;
      priorityFeature = feature;
    }
  }

  return priorityFeature;
}

// --- Exported Functions ---

/**
 * Returns the single, shared socket instance.
 * Creates and connects the socket lazily when first requested.
 * Now supports deferred connection based on feature requirements.
 */
export async function getSocket(
  feature: SocketFeature = "general"
): Promise<Socket> {
  // Register the feature that's requesting the socket
  registerSocketFeature(feature);

  if (!socket) {
    connectionDeferred = false;
    socket = await createSocket();
  } else if (!socket.connected) {
    socket.connect();
  }
  return socket;
}

/**
 * Get socket only if it's already connected (non-blocking)
 * Useful for optional real-time features that shouldn't force connection
 */
export function getSocketIfConnected(): Socket | null {
  return socket?.connected ? socket : null;
}

/**
 * Get connection statistics for monitoring
 */
export function getConnectionStats() {
  return {
    isConnected: socket?.connected || false,
    activeFeatures: Array.from(activeFeatures),
    reconnectAttempts,
    connectionDeferred,
    highestPriorityFeature: getHighestPriorityFeature(),
  };
}

/**
 * Force disconnect socket (for testing or cleanup)
 * Only disconnects if no high-priority features are active
 */
export function forceDisconnect(): boolean {
  const highestPriority = getHighestPriorityFeature();

  // Don't disconnect if high-priority features are active
  if (highestPriority && FEATURE_PRIORITY[highestPriority] <= 2) {
    console.log(
      `[socketClient] Cannot disconnect: high-priority feature '${highestPriority}' is active`
    );
    return false;
  }

  if (socket?.connected) {
    console.log("[socketClient] Force disconnecting socket");
    socket.disconnect();
    activeFeatures.clear();
    return true;
  }

  return false;
}

/**
 * Helper function to emit chapter change with retry logic
 */
export async function emitChapterChange(
  newChapter: number,
  retries = 3
): Promise<boolean> {
  return new Promise(async (resolve) => {
    const socket = await getSocket();

    if (!socket.connected) {
      console.warn("Socket not connected, cannot emit chapter change");
      resolve(false);
      return;
    }

    let attempts = 0;
    const attemptEmit = () => {
      attempts++;

      socket.emit("chapter-changed", { newChapter }, (acknowledgment: any) => {
        if (acknowledgment && acknowledgment.success) {
          console.log("Chapter change acknowledged");
          localStorage.setItem("currentChapter", newChapter.toString());
          resolve(true);
        } else if (attempts < retries) {
          console.log(
            `Chapter change not acknowledged, retrying (${attempts}/${retries})`
          );
          setTimeout(attemptEmit, 1000);
        } else {
          console.error("Chapter change failed after retries");
          resolve(false);
        }
      });
    };

    attemptEmit();
  });
}

/**
 * Helper function to get current chapter from localStorage
 */
export function getCurrentChapter(): number | null {
  const stored = localStorage.getItem("currentChapter");
  return stored ? parseInt(stored) : null;
}

/**
 * Helper function to clear stored chapter data
 */
export function clearStoredChapter(): void {
  localStorage.removeItem("currentChapter");
}

/**
 * Disconnects the shared socket instance.
 * The instance is preserved and can be reconnected later by calling getSocket() or socket.connect().
 */
export function disconnectSocket(): void {
  if (socket && socket.connected) {
    socket.disconnect();
    // CRITICAL: We DO NOT set the instance to null. This preserves the singleton and its event listeners.
  }
}

// --- Step 12: Helper Functions for Connection Quality Monitoring ---

function startPingMonitoring() {
  if (pingInterval) return; // Already running
  console.log("[socketClient] Starting connection quality monitoring.");
  pingInterval = setInterval(async () => {
    if (socket && socket.connected) {
      socket.emit("client-ping", { startTime: Date.now() });
    }
  }, PING_INTERVAL_MS);
}

function stopPingMonitoring() {
  if (pingInterval) {
    console.log("[socketClient] Stopping connection quality monitoring.");
    clearInterval(pingInterval);
    pingInterval = null;
    latencySamples.length = 0; // Clear samples
    // Reset metrics
    connectionQuality.latency = -1;
    connectionQuality.jitter = -1;
    connectionQuality.lastUpdated = 0;
  }
}

function calculateStandardDeviation(arr: number[]): number {
  if (arr.length < 2) return 0;
  const mean = arr.reduce((acc, val) => acc + val, 0) / arr.length;
  const variance =
    arr.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / arr.length;
  return Math.sqrt(variance);
}
