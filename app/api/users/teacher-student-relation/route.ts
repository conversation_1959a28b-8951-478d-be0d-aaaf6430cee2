import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import db from "@/db/drizzle";
import { teacherStudents } from "@/db/schema";
import { eq } from "drizzle-orm";

export async function GET(request: NextRequest) {
  try {
    const { userId } = auth();

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check for teacher-student relationship
    const relation = await db.query.teacherStudents.findFirst({
      where: eq(teacherStudents.studentId, userId),
    });

    if (!relation) {
      return NextResponse.json(
        { error: "No teacher-student relation found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      studentId: relation.studentId,
      teacherId: relation.teacherId,
      joinedAt: (relation as any).joinedAt || new Date().toISOString(),
    });
  } catch (error) {
    console.error("[API] Error fetching teacher-student relation:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
