"use client";

import dynamic from "next/dynamic";
import { useState, useCallback, useEffect } from "react";
import { SidebarCore } from "./sidebar-core";

// Lazy load advanced sidebar features
const SidebarAdvanced = dynamic(
  () =>
    import("./sidebar-advanced").then((mod) => ({
      default: mod.SidebarAdvanced,
    })),
  {
    ssr: false,
    loading: () => (
      <div className="mt-6 space-y-4 animate-pulse">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded" />
        <div className="space-y-2">
          <div className="h-8 bg-gray-100 dark:bg-gray-800 rounded" />
          <div className="h-8 bg-gray-100 dark:bg-gray-800 rounded" />
          <div className="h-8 bg-gray-100 dark:bg-gray-800 rounded" />
        </div>
      </div>
    ),
  }
);

/**
 * Progressive Sidebar - Implements progressive enhancement for sidebar features
 *
 * Strategy:
 * 1. Load core navigation immediately (critical path)
 * 2. Load advanced features after a short delay or user interaction
 * 3. Defer socket connections until messaging features are accessed
 *
 * This approach reduces initial bundle size and improves time to interactive
 */
export const ProgressiveSidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [advancedFeaturesLoaded, setAdvancedFeaturesLoaded] = useState(false);
  const [socketInitialized, setSocketInitialized] = useState(false);

  const handleToggleCollapse = useCallback(() => {
    setIsCollapsed((prev) => !prev);
  }, []);

  const handleLoadAdvancedFeatures = useCallback(() => {
    if (!advancedFeaturesLoaded) {
      setAdvancedFeaturesLoaded(true);
    }
  }, [advancedFeaturesLoaded]);

  // Auto-load advanced features after initial render
  useEffect(() => {
    const timer = setTimeout(() => {
      handleLoadAdvancedFeatures();
    }, 500); // Small delay to prioritize critical rendering

    return () => clearTimeout(timer);
  }, [handleLoadAdvancedFeatures]);

  const handleMessagingInteraction = useCallback(async () => {
    if (!socketInitialized) {
      setSocketInitialized(true);

      try {
        const { getSocket, unregisterSocketFeature } = await import(
          "@/lib/socketClient"
        );
        const socket = await getSocket("unread-counts");

        const handleUnreadUpdate = () => {
          // Invalidate React Query cache to refetch unread counts
          // This is more efficient than the previous polling approach
        };

        socket.on("unread-update", handleUnreadUpdate);

        // Cleanup function
        return () => {
          socket.off("unread-update");
          unregisterSocketFeature("unread-counts");
        };
      } catch (error) {
        console.error("[ProgressiveSidebar] Error initializing socket:", error);
      }
    }
  }, [socketInitialized]);

  return (
    <SidebarCore
      isCollapsed={isCollapsed}
      onToggleCollapse={handleToggleCollapse}
      onLoadAdvancedFeatures={handleLoadAdvancedFeatures}
      advancedContent={
        advancedFeaturesLoaded ? (
          <SidebarAdvanced
            isCollapsed={isCollapsed}
            onMessagingInteraction={handleMessagingInteraction}
          />
        ) : null
      }
    />
  );
};
