// lib/socketClient.ts

"use client";

import type { Socket } from "socket.io-client";

// Adjust URL if your Socket.io server runs elsewhere
const SERVER_URL = typeof window !== "undefined" ? window.location.origin : "";

// --- Lazy Socket Instance ---
// Socket is now created only when first requested, not on module import
let socket: Socket | null = null;
let isConnecting = false;
let eventListenersSetup = false;

// State for the single socket instance, used for logging and state tracking.
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;

// ================================================================== //
// ENHANCEMENTS: State for Queue Status & Connection Quality
// ================================================================== //

// --- Step 11: Queue Status State ---
// This object holds the live status of the server-side message queue.
// It is exported so components can read the current status directly.
export const queueStatus = {
  isQueued: false,
  message: "ok",
};

// --- Step 12: Connection Quality Monitoring State ---
// This object holds live metrics about the connection quality.
// It is exported so components can read the current status directly.
export const connectionQuality = {
  latency: -1, // in ms, -1 for unknown
  jitter: -1, // in ms, -1 for unknown
  reconnects: 0,
  lastUpdated: 0,
};

const latencySamples: number[] = [];
let pingInterval: NodeJS.Timeout | null = null;
const PING_INTERVAL_MS = 5000; // Ping every 5 seconds
const MAX_LATENCY_SAMPLES = 10;

// Create socket instance with optimized settings
async function createSocket(): Promise<Socket> {
  if (socket && socket.connected) {
    return socket;
  }

  if (isConnecting) {
    // Wait for existing connection attempt
    return new Promise((resolve) => {
      const checkConnection = () => {
        if (socket && socket.connected) {
          resolve(socket);
        } else {
          setTimeout(checkConnection, 100);
        }
      };
      checkConnection();
    });
  }

  isConnecting = true;

  try {
    // Dynamic import of socket.io-client
    const { io } = await import("socket.io-client");

    socket = io(SERVER_URL, {
      transports: ["websocket", "polling"],
      timeout: 20000,
      forceNew: false,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      reconnectionAttempts: 5,
    });

    // Setup event listeners only once
    if (!eventListenersSetup) {
      setupEventListeners(socket);
      eventListenersSetup = true;
    }

    return socket;
  } finally {
    isConnecting = false;
  }
}

// --- Event Listeners Setup Function ---
function setupEventListeners(socketInstance: Socket) {
  socketInstance.on("connect", () => {
    console.log("Socket connected with id:", socketInstance.id);
    reconnectAttempts = 0; // Reset counter on successful connection

    // Step 11 & 12: Reset status on new connection
    queueStatus.isQueued = false;
    queueStatus.message = "ok";
    connectionQuality.reconnects = 0;
    window.dispatchEvent(
      new CustomEvent("socket-queue-status-change", { detail: queueStatus })
    );
    startPingMonitoring(); // Start connection quality monitoring

    // Enhanced register with retry logic
    const userId = localStorage.getItem("userId") || "guest_user";
    const role = localStorage.getItem("userRole") || "student";
    const currentChapter = localStorage.getItem("currentChapter");

    socketInstance.emit("register", {
      userId,
      role,
      timestamp: Date.now(),
      ...(currentChapter && { currentChapter: parseInt(currentChapter) }),
    });

    console.log("[socketClient] Sent 'register' event:", {
      userId,
      role,
      currentChapter,
    });

    // Request current chapter sync on reconnect
    if (currentChapter) {
      socketInstance.emit("request-chapter-sync", {
        requestedChapter: parseInt(currentChapter),
      });
    }
  });

  socketInstance.on("disconnect", (reason) => {
    console.log("Socket disconnected:", reason);
    // Step 12: Stop monitoring on disconnect to prevent errors
    stopPingMonitoring();

    // Reconnection is handled automatically by the `reconnection: true` option.
    // We can add custom logic here if needed, for example, to show a UI indicator.
    if (reason === "io client disconnect") {
      console.log(
        "Client manually disconnected. Will not auto-reconnect until getSocket() or socket.connect() is called."
      );
    } else {
      console.log(
        "Disconnection was unexpected. Reconnection will be attempted automatically."
      );
    }
  });

  socketInstance.on("connect_error", (error) => {
    console.error("Socket connection error:", error.message);
    reconnectAttempts++;
    // Step 12: Update connection quality metric
    connectionQuality.reconnects = reconnectAttempts;
  });

  socketInstance.on("reconnect", (attemptNumber) => {
    console.log("Socket reconnected after", attemptNumber, "attempts");
    reconnectAttempts = 0;
    // Step 11: After reconnecting, the queue is likely flushed.
    queueStatus.isQueued = false;
    queueStatus.message = "flushed";
    window.dispatchEvent(
      new CustomEvent("socket-queue-status-change", { detail: queueStatus })
    );
  });

  socketInstance.on("reconnect_failed", () => {
    console.error(
      `Socket reconnection failed after ${MAX_RECONNECT_ATTEMPTS} attempts.`
    );
    // Step 12: Final state on failure
    stopPingMonitoring();
  });

  // ================================================================== //
  // ENHANCEMENTS: New Event Handlers for Queue & Quality
  // ================================================================== //

  // --- Step 11: Handler for OpenAI Reconnecting (Queue Status Indicator) ---
  // This event is sent from the server when it loses its connection to OpenAI
  // and starts queueing messages.
  socketInstance.on(
    "openai-reconnecting",
    (data: { attempt: number; delay: number }) => {
      console.warn(
        `[socketClient] Server is queueing messages. Reconnecting to OpenAI (Attempt ${data.attempt})`
      );
      queueStatus.isQueued = true;
      queueStatus.message = `reconnecting (attempt ${data.attempt})`;
      // Dispatch a global event for components to listen to
      window.dispatchEvent(
        new CustomEvent("socket-queue-status-change", { detail: queueStatus })
      );
    }
  );

  // --- Step 12: Handler for Server Ping Response ---
  // Listens for the 'pong' from the server to calculate latency.
  socketInstance.on("server-pong", (data: { startTime: number }) => {
    const latency = Date.now() - data.startTime;
    latencySamples.push(latency);
    if (latencySamples.length > MAX_LATENCY_SAMPLES) {
      latencySamples.shift(); // Keep only the last N samples
    }

    // Update connection quality metrics
    connectionQuality.latency = latency;
    connectionQuality.jitter = calculateStandardDeviation(latencySamples);
    connectionQuality.lastUpdated = Date.now();

    // Dispatch a global event for components to listen to
    window.dispatchEvent(
      new CustomEvent("socket-quality-update", { detail: connectionQuality })
    );
  });

  // -----------------------------------------------
  // Keep existing annotation logic
  // -----------------------------------------------
  socketInstance.on("cursor-update", (data) => {
    // Example: console.log("Received cursor-update:", data);
  });

  socketInstance.on("highlight-update", (data) => {
    // Example: console.log("Received highlight-update:", data);
  });

  // Enhanced chapter-changed handler with validation
  socketInstance.on("chapter-changed", (data) => {
    console.log("[socketClient] 'chapter-changed' received:", data);

    if (data && typeof data.newChapter === "number") {
      // Store current chapter for reconnection sync
      localStorage.setItem("currentChapter", data.newChapter.toString());

      // Emit chapter sync confirmation
      socketInstance.emit("chapter-sync-confirmed", {
        chapter: data.newChapter,
        timestamp: Date.now(),
      });
    }
  });

  // Handle chapter sync responses
  socketInstance.on("chapter-sync-response", (data) => {
    console.log("[socketClient] 'chapter-sync-response' received:", data);

    if (data && data.currentChapter) {
      localStorage.setItem("currentChapter", data.currentChapter.toString());

      // Trigger a custom event that components can listen to
      window.dispatchEvent(
        new CustomEvent("socket-chapter-sync", {
          detail: { chapter: data.currentChapter },
        })
      );
    }
  });

  // ADDED: meeting-started
  socketInstance.on("meeting-started", (data) => {
    console.log("[socketClient] 'meeting-started' received:", data);
    // Could handle logic here or let your component handle it
  });

  // NEW: Listen for new-message events (optional, depending on your client logic)
  socketInstance.on("new-message", (data) => {
    console.log("[socketClient] 'new-message' received:", data);
  });

  // NEW: Listen for unread-update events to update unread count in real time
  socketInstance.on("unread-update", (data) => {
    console.log("[socketClient] 'unread-update' received:", data);
    // Handle the unread count update, e.g., update global state or notify components
  });

  // Step 1: Add a specific handler for the OpenAI conversation closure.
  // This event is proxied from the server when the OpenAI Realtime API session ends.
  // By handling it here without disconnecting, we keep the main client-server
  // socket.io connection alive for subsequent interactions.
  socketInstance.on("openai-conversation-closed", (data) => {
    console.log(
      "[socketClient] 'openai-conversation-closed' received:",
      (data && data.reason) || "Conversation ended."
    );
    // CRITICAL: We do NOT call disconnectSocket() here.
    // This allows the primary socket.io connection to persist even
    // after a specific AI conversation turn has concluded.
  });

  // Force-refresh event for critical updates
  socketInstance.on("force-refresh", (data) => {
    console.log("[socketClient] 'force-refresh' received:", data);

    if (
      data.reason === "database-reset" ||
      data.reason === "chapter-data-updated"
    ) {
      // Clear any cached data
      localStorage.removeItem("currentChapter");

      // Trigger page refresh or reload chapter data
      window.dispatchEvent(
        new CustomEvent("socket-force-refresh", {
          detail: data,
        })
      );
    }
  });
}

// --- Exported Functions ---

/**
 * Returns the single, shared socket instance.
 * Creates and connects the socket lazily when first requested.
 */
export async function getSocket(): Promise<Socket> {
  if (!socket) {
    socket = await createSocket();
  } else if (!socket.connected) {
    socket.connect();
  }
  return socket;
}

/**
 * Helper function to emit chapter change with retry logic
 */
export async function emitChapterChange(
  newChapter: number,
  retries = 3
): Promise<boolean> {
  return new Promise(async (resolve) => {
    const socket = await getSocket();

    if (!socket.connected) {
      console.warn("Socket not connected, cannot emit chapter change");
      resolve(false);
      return;
    }

    let attempts = 0;
    const attemptEmit = () => {
      attempts++;

      socket.emit("chapter-changed", { newChapter }, (acknowledgment: any) => {
        if (acknowledgment && acknowledgment.success) {
          console.log("Chapter change acknowledged");
          localStorage.setItem("currentChapter", newChapter.toString());
          resolve(true);
        } else if (attempts < retries) {
          console.log(
            `Chapter change not acknowledged, retrying (${attempts}/${retries})`
          );
          setTimeout(attemptEmit, 1000);
        } else {
          console.error("Chapter change failed after retries");
          resolve(false);
        }
      });
    };

    attemptEmit();
  });
}

/**
 * Helper function to get current chapter from localStorage
 */
export function getCurrentChapter(): number | null {
  const stored = localStorage.getItem("currentChapter");
  return stored ? parseInt(stored) : null;
}

/**
 * Helper function to clear stored chapter data
 */
export function clearStoredChapter(): void {
  localStorage.removeItem("currentChapter");
}

/**
 * Disconnects the shared socket instance.
 * The instance is preserved and can be reconnected later by calling getSocket() or socket.connect().
 */
export function disconnectSocket(): void {
  if (socket && socket.connected) {
    socket.disconnect();
    // CRITICAL: We DO NOT set the instance to null. This preserves the singleton and its event listeners.
  }
}

// --- Step 12: Helper Functions for Connection Quality Monitoring ---

function startPingMonitoring() {
  if (pingInterval) return; // Already running
  console.log("[socketClient] Starting connection quality monitoring.");
  pingInterval = setInterval(async () => {
    if (socket && socket.connected) {
      socket.emit("client-ping", { startTime: Date.now() });
    }
  }, PING_INTERVAL_MS);
}

function stopPingMonitoring() {
  if (pingInterval) {
    console.log("[socketClient] Stopping connection quality monitoring.");
    clearInterval(pingInterval);
    pingInterval = null;
    latencySamples.length = 0; // Clear samples
    // Reset metrics
    connectionQuality.latency = -1;
    connectionQuality.jitter = -1;
    connectionQuality.lastUpdated = 0;
  }
}

function calculateStandardDeviation(arr: number[]): number {
  if (arr.length < 2) return 0;
  const mean = arr.reduce((acc, val) => acc + val, 0) / arr.length;
  const variance =
    arr.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / arr.length;
  return Math.sqrt(variance);
}
