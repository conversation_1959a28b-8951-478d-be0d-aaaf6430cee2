// components/CustomCursor.tsx
"use client";

import { useEffect, useState } from "react";
import { Motion } from "@/components/motion-wrapper";
import Image from "next/image";

// For motion values, we'll use regular state since we're using dynamic imports
// and can't rely on useMotionValue being available immediately

/**
 * How big (in CSS pixels) the cursor should appear on-screen,
 * regardless of the native size of Cursor.svg in /public.
 */
const CURSOR_SIZE = 60;

/**
 * Hot-spot tweak: shift the graphic so the arrow’s tip—not its
 * top-left corner—is the actual click point.  Adjust to taste.
 */
const HOTSPOT_OFFSET = 4;

export default function CustomCursor() {
  // Track the raw mouse position using regular state
  const [x, setX] = useState(-100);
  const [y, setY] = useState(-100);

  useEffect(() => {
    const handleMove = (e: MouseEvent) => {
      setX(e.clientX - HOTSPOT_OFFSET);
      setY(e.clientY - HOTSPOT_OFFSET);
    };
    window.addEventListener("mousemove", handleMove);
    return () => window.removeEventListener("mousemove", handleMove);
  }, []);

  return (
    <>
      {/* Hide the system cursor everywhere */}
      <style jsx global>{`
        * {
          cursor: none !important;
        }
      `}</style>

      <Motion.div
        style={{
          transform: `translate(${x}px, ${y}px)`,
        }}
        className="
          fixed left-0 top-0
          pointer-events-none select-none
          z-[2147483647]              /* practically infinite z-index */
        "
      >
        <Image
          src="/Cursor.svg" /* your large SVG in /public */
          alt=""
          width={CURSOR_SIZE}
          height={CURSOR_SIZE}
          draggable={false}
          priority
        />
      </Motion.div>
    </>
  );
}
