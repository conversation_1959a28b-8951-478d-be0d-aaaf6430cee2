"use client";

import React, { useContext, useMemo, useState, useEffect } from "react";
import { EventContext } from "@/app/(main)/schedule/EventContext";
import { getSocket } from "../../../lib/socketClient";
import type { Socket } from "socket.io-client";
import { useRouter } from "next/navigation";
import {
  Video,
  Calendar,
  Users,
  ChevronRight,
  Clock,
  Info,
} from "lucide-react";
import { Button } from "@/components/shadcn-ui/button";
import { Badge } from "@/components/shadcn-ui/badge";
import { Card, CardContent } from "@/components/shadcn-ui/card";
import { Motion } from "@/components/motion-wrapper";

interface MeetingEventsListProps {
  isTeacher: boolean;
  onJoin?: (roomName: string) => void;
}

// Interface matching your CalendarEvent type
interface CalendarEvent {
  id?: number;
  title: string;
  description?: string;
  startTime?: string;
  endTime?: string;
  isMeeting?: boolean;
  jitsiRoomName?: string;
}

export default function MeetingEventsList({
  isTeacher,
  onJoin,
}: MeetingEventsListProps) {
  const { events } = useContext(EventContext);
  const [socket, setSocket] = useState<Socket | null>(null);

  useEffect(() => {
    const initSocket = async () => {
      const socketInstance = await getSocket();
      setSocket(socketInstance);
    };
    initSocket();
  }, []);
  const router = useRouter();
  const [expandedEventId, setExpandedEventId] = useState<number | null>(null);

  // Filter for meeting events
  const meetingEvents = useMemo(
    () => events.filter((evt) => evt.isMeeting),
    [events]
  );

  // Get meeting status
  const getMeetingStatus = (startTime?: string, endTime?: string) => {
    if (!startTime || !endTime) return "upcoming";
    const now = new Date();
    const start = new Date(startTime);
    const end = new Date(endTime);

    if (now < start) return "upcoming";
    if (now > end) return "completed";
    return "in-progress";
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "in-progress":
        return "bg-blue-50 text-blue-700";
      case "completed":
        return "bg-green-50 text-green-700";
      default:
        return "bg-blue-100 text-blue-800";
    }
  };

  // Get time until meeting starts
  const getTimeUntil = (startTime?: string) => {
    if (!startTime) return "";
    const now = new Date();
    const start = new Date(startTime);
    const diff = start.getTime() - now.getTime();
    const minutes = Math.floor(diff / 1000 / 60);

    if (minutes < 0) return "";
    if (minutes < 60) return `Starts in ${minutes}m`;
    return `Starts in ${Math.floor(minutes / 60)}h ${minutes % 60}m`;
  };

  // Handler to start/join meeting - now accepts roomName and title
  const handleJoinMeeting = (roomName: string, title: string) => {
    console.log("[MeetingEventsList] Joining meeting:", { roomName, title });

    if (onJoin) {
      onJoin(roomName);
      return;
    }

    if (isTeacher && socket) {
      socket.emit("meeting-started", { roomName });
    }

    // Now include both the room name and title in the URL
    console.log(
      `[MeetingEventsList] Navigating to /virtualClassroom?room=${roomName}&title=${encodeURIComponent(
        title
      )}`
    );
    router.push(
      `/virtualClassroom?room=${roomName}&title=${encodeURIComponent(title)}`
    );
  };

  if (meetingEvents.length === 0) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-semibold tracking-tight">
            Upcoming Meetings
          </h2>
          <Video className="w-6 h-6 text-gray-500" />
        </div>
        <div className="flex flex-col items-center justify-center h-32 bg-gray-50 rounded-xl">
          <Calendar className="w-8 h-8 text-gray-400 mb-2" />
          <p className="text-gray-500">No meetings scheduled</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-8">
        <h2 className="text-2xl font-semibold tracking-tight">
          Upcoming Meetings
        </h2>
        <Video className="w-6 h-6 text-gray-500" />
      </div>

      <div className="space-y-4">
        {meetingEvents.map((evt) => {
          const status = getMeetingStatus(evt.startTime, evt.endTime);
          const isExpanded = evt.id === expandedEventId;

          return (
            <Motion.div
              key={evt.id || Math.random()}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="group"
            >
              <Card
                className={`relative bg-white overflow-hidden transition-all duration-300 ${
                  isExpanded
                    ? "ring-2 ring-blue-500 ring-opacity-50"
                    : "hover:ring-1 hover:ring-blue-500 hover:ring-opacity-30"
                }`}
              >
                <span className="absolute inset-y-0 left-0 w-1.5 bg-blue-500"></span>

                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="space-y-1 flex-1">
                      <div className="flex items-center space-x-2">
                        {/* Use event.title as the user-friendly display name */}
                        <h3 className="text-lg font-medium text-gray-900">
                          {evt.title}
                        </h3>
                        <Badge
                          variant="secondary"
                          className={`${getStatusColor(
                            status
                          )} text-xs capitalize`}
                        >
                          {status}
                        </Badge>
                      </div>
                      {evt.startTime && (
                        <p className="text-sm text-sky-600 font-medium">
                          {getTimeUntil(evt.startTime)}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        onClick={() =>
                          handleJoinMeeting(
                            evt.jitsiRoomName || "NoRoomName",
                            evt.title
                          )
                        }
                        variant="outline"
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                      >
                        <Video className="w-4 h-4 mr-2" />
                        Join Meeting
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-gray-500 hover:text-gray-700"
                        onClick={() =>
                          setExpandedEventId(isExpanded ? null : evt.id || null)
                        }
                      >
                        <ChevronRight
                          className={`w-5 h-5 transition-transform duration-200 ${
                            isExpanded ? "rotate-90" : ""
                          }`}
                        />
                      </Button>
                    </div>
                  </div>

                  <div
                    className={`space-y-4 transition-all duration-200 ${
                      isExpanded
                        ? "opacity-100"
                        : "opacity-0 h-0 overflow-hidden"
                    }`}
                  >
                    {evt.description && (
                      <div className="flex items-start space-x-2 text-gray-600">
                        <Info className="w-4 h-4 mt-1 flex-shrink-0" />
                        <p className="text-sm">{evt.description}</p>
                      </div>
                    )}

                    {/* Show the random Jitsi name only for teachers (or if you truly want to keep it visible for debugging). */}
                    {isTeacher && evt.jitsiRoomName && (
                      <div className="flex items-center space-x-2 text-gray-600">
                        <Users className="w-4 h-4 flex-shrink-0" />
                        <p className="text-sm">Room: {evt.jitsiRoomName}</p>
                      </div>
                    )}
                  </div>

                  <div className="mt-4 flex items-center text-sm text-gray-500">
                    <Clock className="w-4 h-4 mr-2" />
                    <div className="flex space-x-2">
                      {evt.startTime && (
                        <span>
                          {new Date(evt.startTime).toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </span>
                      )}
                      {evt.endTime && (
                        <>
                          <span>-</span>
                          <span>
                            {new Date(evt.endTime).toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Motion.div>
          );
        })}
      </div>
    </div>
  );
}
