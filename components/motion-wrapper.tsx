"use client";

import dynamic from "next/dynamic";
import { ComponentProps, ReactNode } from "react";

// Create a loading fallback component
const MotionFallback = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => <div className={className}>{children}</div>;

// Dynamically import motion components
const MotionDiv = dynamic(
  () => import("framer-motion").then((mod) => mod.motion.div),
  {
    ssr: false,
    loading: () => <div />,
  }
);

const MotionSpan = dynamic(
  () => import("framer-motion").then((mod) => mod.motion.span),
  {
    ssr: false,
    loading: () => <span />,
  }
);

const AnimatePresenceComponent = dynamic(
  () => import("framer-motion").then((mod) => mod.AnimatePresence),
  {
    ssr: false,
    loading: () => <div />,
  }
);

// Export wrapped components
export const Motion = {
  div: MotionDiv,
  span: MotionSpan,
};

export const AnimatePresence = AnimatePresenceComponent;

// For backward compatibility, export individual components
export { MotionDiv, MotionSpan };
