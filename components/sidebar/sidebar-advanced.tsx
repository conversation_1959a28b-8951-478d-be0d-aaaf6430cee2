"use client";

import { 
  Trophy, 
  ShoppingCart, 
  MessageSquare, 
  Users, 
  Video,
  Calendar as CalendarIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { SidebarItem } from "../sidebar-item";
import { useUnreadCounts } from "@/app/context/UnreadCountsContext";
import { useUser } from "@clerk/nextjs";
import { useCallback } from "react";

interface SidebarAdvancedProps {
  isCollapsed: boolean;
  onMessagingInteraction: () => void;
}

/**
 * Sidebar Advanced - Contains non-critical features that can be loaded after initial render
 * This includes messaging, virtual classroom, schedule, shop, etc.
 * Loading this separately improves initial time to interactive
 */
export const SidebarAdvanced = ({ isCollapsed, onMessagingInteraction }: SidebarAdvancedProps) => {
  const { user } = useUser();
  const { unreadCounts } = useUnreadCounts();
  
  // Check if user is a teacher based on metadata or role
  const isTeacher = user?.publicMetadata?.role === "teacher" || 
                   user?.organizationMemberships?.[0]?.role === "admin";

  const iconSize = isCollapsed ? "h-6 w-6" : "h-4 w-4";

  const distinctSendersWithUnread = Object.values(unreadCounts).filter(
    (count) => count > 0
  ).length;

  return (
    <div className="space-y-6">
      {/* Communication Section */}
      <div className="flex flex-col space-y-1">
        <p
          className={cn(
            "font-serif text-[11px] font-semibold text-[#838381] dark:text-gray-500 uppercase tracking-wider mb-3 px-1 transition-opacity duration-300",
            isCollapsed
              ? "opacity-0 select-none pointer-events-none"
              : "opacity-100"
          )}
        >
          Communication
        </p>
        <div className="space-y-1">
          <SidebarItem
            label="Messages"
            href="/messages"
            badge={distinctSendersWithUnread}
            isCollapsed={isCollapsed}
            onMouseEnter={onMessagingInteraction}
            onClick={onMessagingInteraction}
          >
            <MessageSquare className={cn(iconSize)} />
          </SidebarItem>
          {isTeacher && (
            <SidebarItem
              label="My Students"
              href="/my-students"
              isCollapsed={isCollapsed}
            >
              <Users className={cn(iconSize)} />
            </SidebarItem>
          )}
          <SidebarItem
            label="Virtual Classroom"
            href="/virtualClassroom"
            isCollapsed={isCollapsed}
          >
            <Video className={cn(iconSize)} />
          </SidebarItem>
        </div>
      </div>

      {/* Tools Section */}
      <div className="flex flex-col space-y-1">
        <p
          className={cn(
            "font-serif text-[11px] font-semibold text-[#838381] dark:text-gray-500 uppercase tracking-wider mb-3 px-1 transition-opacity duration-300",
            isCollapsed
              ? "opacity-0 select-none pointer-events-none"
              : "opacity-100"
          )}
        >
          Tools
        </p>
        <div className="space-y-1">
          <SidebarItem
            label="Schedule"
            href="/schedule"
            isCollapsed={isCollapsed}
          >
            <CalendarIcon className={cn(iconSize)} />
          </SidebarItem>
          <SidebarItem 
            label="Shop" 
            href="/shop" 
            isCollapsed={isCollapsed}
          >
            <ShoppingCart className={cn(iconSize)} />
          </SidebarItem>
          {isTeacher && (
            <SidebarItem
              label="Reports"
              href="/leaderboard"
              isCollapsed={isCollapsed}
            >
              <Trophy className={cn(iconSize)} />
            </SidebarItem>
          )}
        </div>
      </div>

      {/* Additional Learning Tools */}
      <div className="flex flex-col space-y-1">
        <p
          className={cn(
            "font-serif text-[11px] font-semibold text-[#838381] dark:text-gray-500 uppercase tracking-wider mb-3 px-1 transition-opacity duration-300",
            isCollapsed
              ? "opacity-0 select-none pointer-events-none"
              : "opacity-100"
          )}
        >
          Advanced
        </p>
        <div className="space-y-1">
          <SidebarItem
            label="Memorization"
            href="/memorization"
            isCollapsed={isCollapsed}
          >
            <svg
              viewBox="0 0 256 256"
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              aria-hidden="true"
              className={cn(iconSize)}
            >
              <rect fill="none" height="256" width="256" />
              <circle cx="76" cy="76" r="44" />
              <circle cx="180" cy="76" r="44" />
              <circle cx="76" cy="180" r="44" />
              <circle cx="180" cy="180" r="44" />
            </svg>
          </SidebarItem>
        </div>
      </div>
    </div>
  );
};
