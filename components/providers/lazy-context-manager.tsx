"use client";

import dynamic from "next/dynamic";
import { ReactNode, useState, useEffect } from "react";

// Lazy load context providers only when they're needed
const EventProvider = dynamic(
  () =>
    import("@/app/(main)/schedule/EventContext").then((mod) => ({
      default: mod.EventProvider,
    })),
  {
    ssr: false,
    loading: () => null, // No loading state needed for context providers
  }
);

const UnreadCountsProvider = dynamic(
  () =>
    import("@/app/context/UnreadCountsContext").then((mod) => ({
      default: mod.UnreadCountsProvider,
    })),
  {
    ssr: false,
    loading: () => null,
  }
);

interface LazyContextManagerProps {
  children: ReactNode;
}

/**
 * Lazy Context Manager - Progressively loads context providers
 *
 * Strategy:
 * 1. UnreadCountsProvider loads immediately (lightweight, needed for sidebar)
 * 2. EventProvider loads after a delay (heavy, only needed for schedule page)
 *
 * This reduces initial bundle size and improves time to interactive
 */
export const LazyContextManager = ({ children }: LazyContextManagerProps) => {
  const [loadEventProvider, setLoadEventProvider] = useState(false);

  // Load EventProvider after initial render to avoid blocking
  useEffect(() => {
    // Defer EventProvider loading until after initial paint
    const timer = setTimeout(() => {
      setLoadEventProvider(true);
    }, 100); // Small delay to prioritize critical rendering

    return () => clearTimeout(timer);
  }, []);

  // Progressive enhancement: Load EventProvider immediately if user navigates to schedule
  useEffect(() => {
    const checkForSchedulePage = () => {
      if (
        typeof window !== "undefined" &&
        window.location.pathname.includes("/schedule")
      ) {
        setLoadEventProvider(true);
      }
    };

    checkForSchedulePage();

    // Listen for navigation changes
    const handlePopState = () => checkForSchedulePage();
    window.addEventListener("popstate", handlePopState);

    return () => window.removeEventListener("popstate", handlePopState);
  }, []);

  // Render with progressive context loading
  if (loadEventProvider) {
    return (
      <EventProvider>
        <UnreadCountsProvider>{children}</UnreadCountsProvider>
      </EventProvider>
    );
  }

  // Initially render with just the lightweight context
  return <UnreadCountsProvider>{children}</UnreadCountsProvider>;
};
