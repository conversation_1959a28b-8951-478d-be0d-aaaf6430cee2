"use client";

import Link from "next/link";
import Image from "next/image";
import { GraduationCap, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { SidebarItem } from "../sidebar-item";
import { useEffect, useState, useRef } from "react";
import { useUser, ClerkLoading, ClerkLoaded } from "@clerk/nextjs";

interface SidebarCoreProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  onLoadAdvancedFeatures: () => void;
  advancedContent?: React.ReactNode;
}

/**
 * Sidebar Core - Contains only the most critical navigation items
 * This loads immediately to provide basic navigation functionality
 * Advanced features are loaded separately to improve initial load time
 */
export const SidebarCore = ({
  isCollapsed,
  onToggleCollapse,
  onLoadAdvancedFeatures,
  advancedContent,
}: SidebarCoreProps) => {
  const { user, isLoaded } = useUser();
  const [hasInteracted, setHasInteracted] = useState(false);

  // Load advanced features when user interacts with sidebar
  const handleInteraction = () => {
    if (!hasInteracted) {
      setHasInteracted(true);
      onLoadAdvancedFeatures();
    }
  };

  const iconSize = isCollapsed ? "h-6 w-6" : "h-4 w-4";

  return (
    <div
      className={cn(
        "h-full bg-white dark:bg-gray-950 border-r border-gray-200 dark:border-gray-800 flex flex-col transition-all duration-300 ease-in-out relative",
        isCollapsed ? "w-16" : "w-64"
      )}
      onMouseEnter={handleInteraction}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-800">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <Link href="/learn" className="flex items-center space-x-2">
              <Image src="/mascot.svg" height={40} width={40} alt="Mascot" />
              <h1 className="text-2xl font-extrabold text-green-600 tracking-wide">
                Lingo
              </h1>
            </Link>
          )}
          <button
            onClick={onToggleCollapse}
            className={cn(
              "p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",
              isCollapsed && "mx-auto"
            )}
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </button>
        </div>
      </div>

      {/* Critical Navigation - Always loaded */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="flex flex-col space-y-1">
          <p
            className={cn(
              "font-serif text-[11px] font-semibold text-[#838381] dark:text-gray-500 uppercase tracking-wider mb-3 px-1 transition-opacity duration-300",
              isCollapsed
                ? "opacity-0 select-none pointer-events-none"
                : "opacity-100"
            )}
          >
            Learning
          </p>
          <div className="space-y-1">
            <SidebarItem label="Learn" href="/learn" isCollapsed={isCollapsed}>
              <GraduationCap className={cn(iconSize)} />
            </SidebarItem>
            <SidebarItem
              label="Learning Path"
              href="/learning-path"
              isCollapsed={isCollapsed}
            >
              <GraduationCap className={cn(iconSize)} />
            </SidebarItem>
          </div>
        </div>

        {/* Advanced features or loading placeholder */}
        {advancedContent || (
          <div className="mt-6 space-y-4">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
            <div className="space-y-2">
              <div className="h-8 bg-gray-100 dark:bg-gray-800 rounded animate-pulse" />
              <div className="h-8 bg-gray-100 dark:bg-gray-800 rounded animate-pulse" />
            </div>
          </div>
        )}
      </div>

      {/* User Info - Critical */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-800">
        <ClerkLoading>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />
            {!isCollapsed && (
              <div className="flex-1">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-1" />
                <div className="h-3 bg-gray-100 dark:bg-gray-800 rounded animate-pulse w-2/3" />
              </div>
            )}
          </div>
        </ClerkLoading>
        <ClerkLoaded>
          {user && (
            <div className="flex items-center space-x-3">
              <Image
                src={user.imageUrl}
                alt={user.firstName || "User"}
                width={32}
                height={32}
                className="rounded-full"
              />
              {!isCollapsed && (
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {user.firstName} {user.lastName}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {user.primaryEmailAddress?.emailAddress}
                  </p>
                </div>
              )}
            </div>
          )}
        </ClerkLoaded>
      </div>
    </div>
  );
};
