import React, { useState, useEffect, useCallback } from "react";
import { challengeOptions } from "@/db/schema";
import { Card } from "./card";
import { AudioButton } from "./AudioButton";
import Image from "next/image";

type ChallengeType =
  | "SELECT"
  | "ASSIST"
  | "MATCHING"
  | "TAP_WHAT_YOU_HEAR"
  | "IMAGE_AUDIO_SELECT"
  | "DRAG_AND_DROP";

type Question = {
  audioSrc: string;
  text: string;
};

type Props = {
  options: (typeof challengeOptions.$inferSelect)[];
  onSelect: (result: number[], challengeZoneSequences: number[]) => void;
  status: "correct" | "wrong" | "none" | "submitting";
  disabled?: boolean;
  question: Question;
  type: ChallengeType;
  mediaType?: string | null;
  mediaUrl?: string | null;
  topCardText?: string | null;
  topCardAudio?: string | null;
  sentence?: string | null;
  attempt?: number;
};

export const DragAndDrop = ({
  options,
  onSelect,
  status,
  disabled,
  question,
  type,
  mediaType,
  mediaUrl,
  topCardText,
  topCardAudio,
  sentence,
  attempt,
}: Props) => {
  const [selectedOptions, setSelectedOptions] = useState<number[]>([]);
  const [challengeZoneOptions, setChallengeZoneOptions] = useState<number[]>(
    []
  );
  const [shuffledOptions, setShuffledOptions] = useState<(typeof options)[0][]>(
    []
  );
  const [showHint, setShowHint] = useState(false);
  const [isShuffling, setIsShuffling] = useState(false);

  const shuffleOptions = (array: typeof options) => {
    const shuffled = [...array].sort(() => Math.random() - 0.5);
    return shuffled;
  };

  useEffect(() => {
    setShuffledOptions(shuffleOptions(options));
    setChallengeZoneOptions(options.map((option) => option.id));
  }, [options]);

  const handleOptionClick = useCallback(
    (id: number) => {
      if (disabled) return;

      setSelectedOptions((prev) => {
        const newOptions = prev.includes(id)
          ? prev.filter((opt) => opt !== id)
          : [...prev, id];

        setChallengeZoneOptions(
          shuffledOptions
            .filter((option) => !newOptions.includes(option.id))
            .map((option) => option.id)
        );

        return newOptions;
      });
    },
    [disabled, shuffledOptions]
  );

  useEffect(() => {
    if (selectedOptions.length === 0) return;

    const challengeZoneSequences = challengeZoneOptions.map(
      (id) => shuffledOptions.find((option) => option.id === id)?.sequence ?? 0
    );
    onSelect(selectedOptions, challengeZoneSequences);
  }, [selectedOptions, challengeZoneOptions, onSelect, shuffledOptions]);

  useEffect(() => {
    if (status === "wrong") {
      setSelectedOptions([]);
      setChallengeZoneOptions(shuffledOptions.map((option) => option.id));
      if (!showHint) {
        setShowHint(true);
      }
    }
  }, [status, shuffledOptions, showHint]);

  useEffect(() => {
    if (attempt !== undefined) {
      setIsShuffling(true);
      setShowHint(false);
      setTimeout(() => {
        setShuffledOptions(shuffleOptions(options));
        setChallengeZoneOptions(options.map((option) => option.id));
        setSelectedOptions([]);
        setIsShuffling(false);
      }, 500);
    }
  }, [attempt, options]);

  return (
    <div className="min-h-0 flex flex-col pt-2">
      <div className="flex justify-between items-center px-4 mb-40">
        <p className="text-lg font-medium">{question.text}</p>
        <AudioButton src={question.audioSrc} />
      </div>
      {mediaType && mediaUrl && (
        <div className="mb-4">
          {mediaType === "image" && (
            <Image
              src={mediaUrl}
              alt={question.text}
              width={600}
              height={400}
              className="w-full h-auto"
            />
          )}
          {mediaType === "audio" && (
            <audio controls className="w-full">
              <source src={mediaUrl} type="audio/mpeg" />
            </audio>
          )}
          {mediaType === "video" && (
            <video controls className="w-full">
              <source src={mediaUrl} type="video/mp4" />
            </video>
          )}
        </div>
      )}
      {topCardText && (
        <div className="text-center mb-4">
          <p>{topCardText}</p>
          {topCardAudio && (
            <audio controls className="w-full mt-1">
              <source src={topCardAudio} type="audio/mpeg" />
            </audio>
          )}
        </div>
      )}
      <div className="relative w-full h-24 mb-4 flex items-start justify-end pt-8">
        <div className="absolute w-full h-[2px] bg-gray-500 top-1/2 transform -translate-y-1/2" />
        {showHint && sentence && (
          <div className="absolute top-[-40px] text-3xl text-gray-500 font-bold text-center w-full">
            {sentence}
          </div>
        )}
        <div className="flex justify-end gap-1 -mt-20 mr-6">
          {[...selectedOptions].reverse().map((id) => {
            const option = shuffledOptions.find((opt) => opt.id === id);
            return option ? (
              <Card
                key={id}
                id={option.id}
                text={option.text}
                imageSrc={option.imageSrc}
                audioSrc={option.audioSrc}
                type={type}
                onClick={() => handleOptionClick(option.id)}
                selected={true}
                status={status}
                disabled={disabled}
                customClass="relative z-10 w-[80px] h-[80px] p-2 text-xl lg:text-3xl"
                overrideSelectionColor={true}
              />
            ) : null;
          })}
        </div>
      </div>
      <div className="mx-6">
        <div
          className={`flex flex-wrap justify-center gap-2 px-2 pb-2 ${
            isShuffling ? "animate-shuffle" : ""
          }`}
        >
          {shuffledOptions.map((option) => (
            <Card
              key={option.id}
              id={option.id}
              text={option.text}
              imageSrc={option.imageSrc}
              audioSrc={option.audioSrc}
              onClick={() => handleOptionClick(option.id)}
              selected={selectedOptions.includes(option.id)}
              status={status}
              disabled={disabled}
              type={type}
              customClass="w-[100px] h-[100px] p-2 text-xl lg:text-5xl"
            />
          ))}
        </div>
      </div>
      <style jsx>{`
        @keyframes shuffle {
          0% {
            transform: translate(0, 0);
          }
          25% {
            transform: translate(-5px, 5px);
          }
          50% {
            transform: translate(5px, -5px);
          }
          75% {
            transform: translate(-5px, -5px);
          }
          100% {
            transform: translate(0, 0);
          }
        }
        .animate-shuffle {
          animation: shuffle 0.5s ease-in-out;
        }
      `}</style>
    </div>
  );
};
