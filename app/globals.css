@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Fonts - Optimized for Performance */
@font-face {
  font-family: "Hafs";
  src: url("/fonts/Hafs.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap; /* Improves font loading performance */
}

@font-face {
  font-family: "Noorehuda";
  src: url("/fonts/Noorehuda-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap; /* Improves font loading performance */
}

/* --- ADDITION: Canva Sans Font Definition --- */
@font-face {
  font-family: "Canva Sans";
  src: url("/fonts/CanvaSans-Bold.woff2") format("woff2"),
    /* Modern format */ url("/fonts/CanvaSans-Bold.ttf") format("truetype"); /* Fallback format */
  font-weight: bold;
  font-style: normal;
  font-display: swap; /* Improves font loading performance */
}

/* Global Styles */
html,
body,
:root {
  @apply h-full min-h-screen;
}

@layer base {
  :root {
    /* Ensure proportional numerals for better visual balance */
    font-feature-settings: "pnum";

    /* Existing Theme Variables */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;

    /* Enhanced Shadow Variables */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1),
      0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
      0 4px 6px -4px rgb(0 0 0 / 0.1);

    /* --- NEW VARIABLES FOR UPGRADE BUTTON --- */
    --sidebar-bg-hsl: var(--background);
    --sidebar-stripe-color-hsl: var(--muted);
  }

  .dark {
    /* Existing Dark Theme Variables */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Enhanced Dark Mode Shadow Variables */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.15);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.2), 0 1px 2px -1px rgb(0 0 0 / 0.2);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.2),
      0 2px 4px -2px rgb(0 0 0 / 0.2);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.2),
      0 4px 6px -4px rgb(0 0 0 / 0.2);

    /* --- NEW VARIABLES FOR UPGRADE BUTTON (DARK) --- */
    --sidebar-bg-hsl: var(--background);
    --sidebar-stripe-color-hsl: var(--border);
  }
}

/* Background Grid Pattern */
.bg-grid {
  background-image: linear-gradient(
      to right,
      rgba(0, 0, 0, 0.1) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 100px 100px;
}

/* Dark mode grid pattern */
.dark .bg-grid {
  background-image: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    cursor: url("/cursor.png"), auto;
    /* Temporarily using ProximaVara instead of Hafs for debugging */
    font-family: "ProximaVara", "Helvetica Neue", "Helvetica", "Arial",
      sans-serif;
    font-feature-settings: "pnum";
    font-variant-numeric: proportional-nums;
  }

  /* Ensure all text elements use consistent font and proportional numerals */
  *,
  span,
  div,
  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  button,
  a {
    font-feature-settings: "pnum" !important;
    font-variant-numeric: proportional-nums !important;
  }

  /* Force lesson numbers to use consistent rendering */
  .lesson-number {
    font-family: "ProximaVara", "Helvetica Neue", "Helvetica", "Arial",
      sans-serif !important;
    font-feature-settings: "pnum" !important;
    font-weight: 600 !important;
    font-variant-numeric: proportional-nums !important;
  }

  /* Arabic content should still use Hafs when specifically needed */
  .arabic-text {
    font-family: "Hafs", sans-serif;
  }

  /* Verse glyphs and Arabic decorative elements need Hafs font */
  .verse-glyph {
    font-family: "Hafs", sans-serif !important;
  }

  /* Ensure Arabic text and glyphs maintain their font */
  .font-hafs {
    font-family: "Hafs", sans-serif !important;
  }
}

/* Enhanced Component Styles */
@layer components {
  /* Card hover effects */
  .hover-card-effect {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  /* Gradient text effect */
  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent;
  }

  /* Glass morphism effect */
  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .dark .glass-effect {
    @apply bg-black/10 border-white/10;
  }

  /* Proportional numbers utility */
  .proportional-nums {
    font-feature-settings: "pnum";
  }

  /* Tabular numbers utility (when needed) */
  .tabular-nums {
    font-feature-settings: "tnum";
  }

  /* Lesson number circle styling for consistent display */
  .lesson-circle {
    font-family: "ProximaVara", "Helvetica Neue", "Helvetica", "Arial",
      sans-serif !important;
    font-feature-settings: "pnum" !important;
    font-weight: 600 !important;
    font-variant-numeric: proportional-nums !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Alternative utility for numbers that need consistent width */
  .balanced-nums {
    font-family: "ProximaVara", "Helvetica Neue", "Helvetica", "Arial",
      sans-serif;
    font-feature-settings: "pnum";
    font-weight: 600;
    font-variant-numeric: proportional-nums;
    text-align: center;
  }
}

/* Enhanced Utility Classes */
@layer utilities {
  /* Text balance for better typography */
  .text-balance {
    text-wrap: balance;
  }

  /* Better scrollbar styling */
  .custom-scrollbar {
    @apply scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 dark:scrollbar-thumb-gray-700 dark:scrollbar-track-gray-900;
  }

  /* Smooth transitions */
  .transition-all-smooth {
    @apply transition-all duration-300 ease-in-out;
  }

  /* --- ADDITION: Canva Sans Utility Class --- */
  .font-canva-sans {
    font-family: "Canva Sans", sans-serif;
  }
}

/* Animation Keyframes */
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.fill-in-blank-container {
  @apply transition-all duration-300;
}

.fill-in-blank-dash {
  @apply inline-block w-2 underline decoration-2 decoration-gray-400;
  text-underline-offset: 8px;
}
