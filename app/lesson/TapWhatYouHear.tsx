import { useEffect, useCallback, useState } from "react";
import { AudioButton } from "./AudioButton";
import { Card } from "./card";
import Image from "next/image";

// Define Props correctly
type Props = {
  options: {
    id: number;
    text: string;
    imageSrc?: string | null;
    audioSrc?: string | null;
    correct?: boolean;
  }[];
  onSelect: (id: number | null) => void;
  selectedOption: number | null;
  status: "correct" | "wrong" | "none" | "submitting";
  disabled: boolean;
  sentence: string | null;
  question: string;
  mediaUrl?: string | null;
  audioSrc?: string | null;
  mediaType?: string | null;
  topCardText?: string | null;
  topCardAudio?: string | null;
  type: string;
};

export const TapWhatYouHear = ({
  options,
  onSelect,
  selectedOption,
  status,
  question,
  audioSrc,
  mediaType,
  mediaUrl,
  topCardText,
  topCardAudio,
  sentence,
  disabled,
}: Props) => {
  // Handle the selection logic and ensure that the status is correctly set in the parent
  const handleOptionClick = useCallback(
    (id: number) => {
      if (status === "none") {
        onSelect(id); // Allow selection only when status is "none"
      }
    },
    [onSelect, status]
  );

  return (
    <div className="flex flex-col items-center space-y-4">
      {audioSrc && <AudioButton src={audioSrc} />}
      {mediaType && mediaUrl && (
        <div className="flex justify-center mb-4">
          {mediaType === "image" && (
            <Image
              src={mediaUrl}
              alt="Related media"
              width={400}
              height={300}
              className="max-w-full h-auto"
            />
          )}
          {mediaType === "audio" && (
            <audio controls>
              <source src={mediaUrl} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          )}
          {mediaType === "video" && (
            <video controls className="max-w-full">
              <source src={mediaUrl} type="video/mp4" />
              Your browser does not support the video element.
            </video>
          )}
        </div>
      )}
      {topCardText && (
        <div className="text-center mb-4">
          <p>{topCardText}</p>
          {topCardAudio && (
            <audio controls>
              <source src={topCardAudio} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          )}
        </div>
      )}
      {sentence && (
        <div className="text-center mb-4">
          <p>{sentence}</p>
        </div>
      )}
      <div className="grid grid-cols-2 gap-1 mt-4">
        {options.map((option) => (
          <div key={option.id} className="p-0.5">
            <Card
              id={option.id}
              text={option.text}
              imageSrc={option.imageSrc}
              audioSrc={option.audioSrc}
              onClick={() => handleOptionClick(option.id)}
              selected={selectedOption === option.id}
              status={
                selectedOption === option.id ? status : "none" // Show status for the selected option
              }
              disabled={disabled || status !== "none"} // Disable options if the status is not "none"
              type="TAP_WHAT_YOU_HEAR"
              version="version1"
              customClass="custom-card"
            />
          </div>
        ))}
      </div>
    </div>
  );
};
