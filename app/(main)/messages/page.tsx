"use client";

import React, { useEffect, useState, useRef, useCallback } from "react";
import { useAuth } from "@clerk/nextjs";
import { Socket } from "socket.io-client";
import { getSocket } from "@/lib/socketClient";
import { Search, Send, Paperclip, Link2, MoreVertical } from "lucide-react";
import Image from "next/image";
import { useUnreadCounts } from "@/app/context/UnreadCountsContext";
import Loading from "./loading";

function useRecipientFromURL() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [recipientId, setRecipientId] = useState<string | null>(null);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const id = params.get("recipientId");
    setRecipientId(id);
    setIsLoaded(true);
  }, []);

  return { recipientId, isLoaded };
}

type Message = {
  id?: number;
  content: string;
  senderId?: string;
  recipientId?: string;
  timestamp: string;
};

type Student = {
  id: string;
  name: string;
  avatar: string;
  lastMessage: string;
  timestamp: string;
  unreadCount?: number;
};

let socket: Socket | undefined;

const MessagesPage = () => {
  const { userId } = useAuth();

  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState<string>("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [students, setStudents] = useState<Student[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const { unreadCounts, setUnreadCounts } = useUnreadCounts();

  const { recipientId: urlRecipientId, isLoaded: urlLoaded } =
    useRecipientFromURL();

  async function fetchUserAvatar(userId: string): Promise<string> {
    try {
      const response = await fetch(`/api/users/${userId}`);
      if (response.ok) {
        const userData = await response.json();
        if (userData.avatarSrc) {
          return userData.avatarSrc;
        }
      }
      return "/mascot.svg";
    } catch (error) {
      return "/mascot.svg";
    }
  }

  const fetchRealStudents = useCallback(async () => {
    try {
      const res = await fetch("/api/my-contacts");
      if (!res.ok) {
        throw new Error("Failed to fetch real students");
      }
      const data = await res.json();

      const enhancedData = await Promise.all(
        data.map(async (student: Student) => {
          if (student.avatar && !student.avatar.includes("mascot.svg")) {
            return student;
          }
          const avatarSrc = await fetchUserAvatar(student.id);
          return {
            ...student,
            avatar: avatarSrc,
          };
        })
      );
      setStudents(enhancedData);
    } catch (err) {
      // console.error("[MessagesPage] Error fetching real students:", err);
    }
  }, []);

  const fetchUnreadCounts = useCallback(async () => {
    try {
      const res = await fetch("/api/messages/unread-counts");
      if (!res.ok) {
        throw new Error("Failed to fetch unread counts");
      }
      const data = await res.json();
      const counts: { [senderId: string]: number } = {};
      data.unreadCounts.forEach(
        (item: { senderId: string; unreadCount: number }) => {
          counts[item.senderId] = item.unreadCount;
        }
      );
      setUnreadCounts(counts);
    } catch (err) {
      // console.error("[MessagesPage] Error fetching unread counts:", err);
    }
  }, [setUnreadCounts]);

  const loadChatHistory = useCallback(async (contactId: string) => {
    try {
      const res = await fetch(`/api/messages?recipientId=${contactId}`);
      if (!res.ok) {
        throw new Error("Failed to fetch chat history");
      }
      const data = await res.json();

      const formatted = data.map((dbMsg: any) => ({
        id: dbMsg.id,
        content: dbMsg.content,
        senderId: dbMsg.senderId,
        recipientId: dbMsg.recipientId,
        timestamp: dbMsg.createdAt
          ? new Date(dbMsg.createdAt).toISOString()
          : new Date().toISOString(),
      }));
      setMessages(formatted);
    } catch (err) {
      // console.error("[MessagesPage] Error loading chat history:", err);
    }
  }, []);

  const markConversationRead = useCallback(
    async (contactId: string) => {
      try {
        const res = await fetch("/api/messages/mark-read", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ conversationPartnerId: contactId }),
        });
        if (!res.ok) {
          // console.error("Failed to mark conversation as read");
        } else {
          const result = await res.json();
          // console.log("[MessagesPage] Marked conversation as read:", result);
          fetchUnreadCounts();
        }
      } catch (err) {
        // console.error(
        //   "[MessagesPage] Error marking conversation as read:",
        //   err
        // );
      }
    },
    [fetchUnreadCounts]
  );

  const handleSelectContact = useCallback(
    (student: Student) => {
      setSelectedStudent(student);
      loadChatHistory(student.id);
      markConversationRead(student.id);
    },
    [loadChatHistory, markConversationRead]
  );

  const fetchData = useCallback(async () => {
    await Promise.all([fetchRealStudents(), fetchUnreadCounts()]);
    setLoading(false);
  }, [fetchRealStudents, fetchUnreadCounts]);

  useEffect(() => {
    const initializeSocket = async () => {
      socket = await getSocket();

      socket.on("connect", () => {
        // console.log("[MessagesPage] Socket connected, id=", socket?.id);
      });

      socket.on("message", (incoming: Message) => {
        setMessages((prevMessages) => [...prevMessages, incoming]);
      });

      socket.on("unread-update", (data) => {
        fetchUnreadCounts();
      });
    };

    initializeSocket();

    fetchData().catch((err) => {
      // console.error(err);
      setLoading(false);
    });

    return () => {
      socket?.disconnect();
    };
  }, [fetchData, fetchUnreadCounts]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  async function saveMessageToDB(content: string) {
    try {
      if (!selectedStudent || !userId) return null;
      const recipientId = selectedStudent.id;
      const res = await fetch("/api/messages", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ recipientId, content }),
      });
      if (!res.ok) {
        throw new Error("Failed to save message to DB");
      }
      const savedMsg = await res.json();
      return savedMsg;
    } catch (err) {
      // console.error("[MessagesPage] saveMessageToDB error:", err);
      return null;
    }
  }

  const sendMessage = async () => {
    if (!userId) {
      // console.log("[MessagesPage] No user is logged in, cannot send message.");
      return;
    }
    if (!socket) {
      // console.log("[MessagesPage] Socket is undefined, cannot send message.");
      return;
    }
    if (!selectedStudent) {
      // console.log("[MessagesPage] No contact selected.");
      return;
    }
    if (message.trim()) {
      const saved = await saveMessageToDB(message);
      if (!saved) return;
      const newMessage: Message = {
        id: saved.id,
        content: saved.content,
        senderId: saved.senderId,
        recipientId: saved.recipientId,
        timestamp: new Date(saved.createdAt).toISOString(),
      };
      socket.emit("message", newMessage);
      setMessages((prevMessages) => [...prevMessages, newMessage]);
      setMessage("");
    }
  };

  const handleKeyPress = async (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      await sendMessage();
    }
  };

  function getMessageAlignment(msg: Message) {
    if (!userId) return "left";
    return msg.senderId === userId ? "right" : "left";
  }

  const filteredStudents = students.filter(
    (student) =>
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (student.lastMessage &&
        student.lastMessage.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  useEffect(() => {
    const selectContactFromURL = async () => {
      if (urlLoaded && urlRecipientId && students.length > 0) {
        const foundStudent = students.find((s) => s.id === urlRecipientId);
        if (foundStudent) {
          handleSelectContact(foundStudent);
        } else {
          try {
            const res = await fetch(`/api/users/${urlRecipientId}`);
            if (res.ok) {
              const userData = await res.json();
              const tempStudent: Student = {
                id: userData.userId,
                name: userData.displayName || "Unknown User",
                avatar: "/mascot.svg",
                lastMessage: "No messages yet",
                timestamp: "Now",
              };
              setStudents((prev) => [...prev, tempStudent]);
              handleSelectContact(tempStudent);
            }
          } catch (error) {
            // console.error("[MessagesPage] Error fetching user:", error);
          }
        }
      }
    };
    selectContactFromURL();
  }, [urlLoaded, urlRecipientId, students, handleSelectContact]);

  if (loading || !urlLoaded) {
    return <Loading />;
  }

  return (
    <div className="flex h-screen bg-neutral-100">
      <div className="w-80 border-r border-neutral-200 flex flex-col bg-neutral-50">
        <div className="p-4 border-b border-neutral-200">
          <div className="flex justify-between items-center mb-5">
            <h1 className="text-xl font-medium text-neutral-800">
              All Messages
            </h1>
            <div className="flex gap-2">
              <button className="px-3 py-1.5 bg-black text-neutral-50 rounded-lg text-sm flex items-center hover:bg-neutral-800 active:scale-95 transition-colors duration-200">
                <Send className="w-4 h-4 mr-2 text-neutral-50" />
                Message
              </button>
            </div>
          </div>
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 -translate-y-1/2 text-neutral-400" />
            <input
              type="text"
              placeholder="Search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-neutral-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-neutral-500 transition-colors duration-200"
            />
          </div>
        </div>
        <div className="overflow-y-auto flex-1 bg-white">
          {filteredStudents.length > 0 ? (
            filteredStudents.map((student) => (
              <div
                key={student.id}
                onClick={() => handleSelectContact(student)}
                className={`p-4 hover:bg-neutral-200 cursor-pointer flex items-center transition-colors duration-200 ${
                  selectedStudent?.id === student.id ? "bg-neutral-200" : ""
                }`}
              >
                <Image
                  src={student.avatar}
                  alt={student.name}
                  width={40}
                  height={40}
                  className="w-10 h-10 rounded-lg object-cover flex-shrink-0"
                />
                <div className="flex-1 min-w-0 ml-3">
                  <div className="flex justify-between items-center">
                    <h3 className="font-medium text-neutral-800 truncate">
                      {student.name}
                      {unreadCounts[student.id] > 0 && (
                        <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2">
                          {unreadCounts[student.id]}
                        </span>
                      )}
                    </h3>
                    <span className="text-xs text-neutral-500 ml-2">
                      {student.timestamp}
                    </span>
                  </div>
                  <p className="text-sm text-neutral-500 truncate">
                    {student.lastMessage}
                  </p>
                </div>
              </div>
            ))
          ) : (
            <div className="p-4 text-center text-neutral-500">
              No students found.
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 flex flex-col bg-neutral-50">
        {selectedStudent ? (
          <>
            <div className="p-4 border-b border-neutral-200 flex justify-between items-center bg-neutral-50">
              <div className="flex items-center gap-3">
                <Image
                  src={selectedStudent.avatar}
                  alt={selectedStudent.name}
                  width={40}
                  height={40}
                  className="w-10 h-10 rounded-lg object-cover"
                />
                <div>
                  <h2 className="font-medium text-neutral-800">
                    {selectedStudent.name}
                  </h2>
                  <span className="text-sm text-neutral-500">Online</span>
                </div>
              </div>
              <button className="text-neutral-500 hover:text-neutral-700 transition-colors duration-200">
                <MoreVertical className="w-5 h-5" />
              </button>
            </div>

            <div
              className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50"
              style={{
                backgroundColor: "#ffffff",
                backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
                backgroundRepeat: "repeat",
              }}
            >
              {messages.length > 0 ? (
                messages.map((msg, index) => {
                  const alignment = getMessageAlignment(msg);
                  return (
                    <div
                      key={index}
                      className={`flex ${
                        alignment === "left" ? "justify-start" : "justify-end"
                      }`}
                    >
                      <div
                        className={`max-w-[70%] rounded-xl p-3 mb-1 shadow-[0_2px_8px_rgba(0,0,0,0.08)] hover:shadow-[0_4px_12px_rgba(0,0,0,0.12)] transition-shadow duration-200 ${
                          alignment === "left"
                            ? "bg-neutral-50 text-neutral-800"
                            : "bg-black text-neutral-50"
                        }`}
                      >
                        <p className="text-sm break-words">{msg.content}</p>
                        <span className="text-xs opacity-75 mt-1 block text-right">
                          {new Date(msg.timestamp).toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </span>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="text-center text-neutral-500">
                  No messages yet. Start the conversation!
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            <div className="p-4 border-t border-neutral-200 bg-neutral-50">
              <div className="flex items-center gap-2">
                <div className="flex-1 relative">
                  <textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Type a message..."
                    className="w-full p-3 pr-12 rounded-lg border border-neutral-300 focus:outline-none focus:ring-2 focus:ring-neutral-500 resize-none bg-white text-neutral-800 placeholder-neutral-400 transition-colors duration-200"
                    rows={1}
                  />
                  <div className="absolute right-3 top-1/2 -translate-y-1/2 flex gap-2">
                    <button className="text-neutral-400 hover:text-neutral-600 transition-colors duration-200">
                      <Paperclip className="w-5 h-5" />
                    </button>
                    <button className="text-neutral-400 hover:text-neutral-600 transition-colors duration-200">
                      <Link2 className="w-5 h-5" />
                    </button>
                  </div>
                </div>
                <button
                  onClick={sendMessage}
                  className="p-3 bg-black text-neutral-50 rounded-lg hover:bg-neutral-800 active:scale-95 transition-colors duration-200 flex items-center justify-center"
                >
                  <Send className="w-5 h-5 text-neutral-50" />
                </button>
              </div>
            </div>
          </>
        ) : (
          <div
            className="flex-1 flex items-center justify-center bg-gray-50"
            style={{
              backgroundColor: "#ffffff",
              backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='32' viewBox='0 0 16 32'%3E%3Cg fill='%23efefef' fill-opacity='0.4'%3E%3Cpath fill-rule='evenodd' d='M0 24h4v2H0v-2zm0 4h6v2H0v-2zm0-8h2v2H0v-2zM0 0h4v2H0V0zm0 4h2v2H0V4zm16 20h-6v2h6v-2zm0 4H8v2h8v-2zm0-8h-4v2h4v-2zm0-20h-6v2h6V0zm0 4h-4v2h4V4zm-2 12h2v2h-2v-2zm0-8h2v2h-2V8zM2 8h10v2H2V8zm0 8h10v2H2v-2zm-2-4h14v2H0v-2zm4-8h6v2H4V4zm0 16h6v2H4v-2zM6 0h2v2H6V0zm0 24h2v2H6v-2z'/%3E%3C/g%3E%3C/svg%3E")`,
              backgroundRepeat: "repeat",
            }}
          >
            <div className="text-center text-neutral-500">
              <h2 className="text-lg mb-2 font-medium">
                No Conversation Selected
              </h2>
              <p>Select a conversation from the sidebar to start messaging.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MessagesPage;
