import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { Toaster } from "@/components/ui/sonner";
import { ModalManager } from "@/components/modals/modal-manager";
import { LazyContextManager } from "@/components/providers/lazy-context-manager";
import { ProgressiveEnhancementProvider } from "@/components/progressive-enhancement-provider";
import Script from "next/script";
import "./globals.css";
import { Providers } from "./providers";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Server-side authentication check - but allow public routes
  // We'll handle specific auth requirements in nested layouts

  return (
    <ClerkProvider signInUrl="/" signUpUrl="/">
      <Providers>
        <LazyContextManager>
          <ProgressiveEnhancementProvider>
            <html lang="en">
              <head>
                {/* Font Preloading for Performance */}
                <link
                  rel="preload"
                  href="/fonts/Hafs.ttf"
                  as="font"
                  type="font/ttf"
                  crossOrigin="anonymous"
                />
                <link
                  rel="preload"
                  href="/fonts/Noorehuda-Regular.ttf"
                  as="font"
                  type="font/ttf"
                  crossOrigin="anonymous"
                />
                <Script
                  src="https://meet.jit.si/external_api.js"
                  strategy="afterInteractive"
                />
              </head>
              <body className="font-sans">
                <Toaster />
                <ModalManager />
                {children}
              </body>
            </html>
          </ProgressiveEnhancementProvider>
        </LazyContextManager>
      </Providers>
    </ClerkProvider>
  );
}
