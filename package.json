{"name": "lingo", "version": "0.1.0", "private": true, "scripts": {"dev:server": "node -r dotenv/config -r @swc-node/register server.ts", "dev:next": "next dev", "dev": "npm-run-all --parallel dev:server dev:next", "create-pdf": "npx ts-node scripts/create-test-pdf.ts", "build": "next build", "start": "next start", "lint": "next lint", "db:studio": "npx drizzle-kit studio", "db:push": "npx drizzle-kit push", "db:seed": "tsx ./scripts/seed.ts", "db:prod": "tsx ./scripts/prod.ts", "db:reset": "tsx ./scripts/reset.ts", "generate": "drizzle-kit generate", "migrate": "drizzle-kit migrate", "migrate-data": "tsx -r dotenv/config ./scripts/migrate-data.ts", "test": "node --max-old-space-size=8192 ./node_modules/.bin/jest"}, "dependencies": {"@aws-sdk/client-s3": "^3.709.0", "@clerk/nextjs": "^5.7.2", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/google-calendar": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/rrule": "^6.1.15", "@fullcalendar/scrollgrid": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@headlessui/react": "^2.1.7", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^5.1.1", "@neondatabase/serverless": "^1.0.1", "@prisma/client": "^6.4.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@types/formidable": "^3.4.5", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "docx": "^9.1.0", "docx4js": "3.3.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.36.1", "dynamic-time-warping": "^1.0.0", "esbuild": "0.19.12", "express": "^4.21.2", "ffmpeg-static": "^5.2.0", "formidable": "^3.5.2", "framer-motion": "^11.15.0", "fs": "^0.0.1-security", "googleapis": "^144.0.0", "howler": "^2.2.4", "immer": "^10.1.1", "js-levenshtein": "^1.1.6", "lucide-react": "^0.344.0", "mammoth": "^1.8.0", "matter-js": "^0.20.0", "meyda": "^5.6.3", "motion": "^12.18.1", "next": "^14.2.20", "next-themes": "^0.2.1", "node-wav": "^0.0.2", "openai": "^4.65.0", "p5": "^1.11.3", "pdf-lib": "^1.17.1", "pdf2json": "^3.1.4", "pg-connection-string": "^2.6.4", "postgres": "^3.4.7", "ra-data-simple-rest": "^4.16.12", "react": "^18.3.1", "react-admin": "^5.10.0", "react-audio-player": "^0.17.0", "react-calendar": "^5.0.0", "react-circular-progressbar": "^2.1.0", "react-color": "^2.19.3", "react-confetti": "^6.1.0", "react-day-picker": "^9.8.0", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-icons": "^5.4.0", "react-modal": "^3.16.1", "react-pdf": "^9.1.1", "react-swipeable": "^7.0.1", "react-use": "^17.5.0", "reactflow": "^11.11.4", "rrule": "^2.8.1", "sass": "^1.86.0", "simplex-noise": "^4.0.3", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "sonner": "^1.4.3", "stripe": "^14.20.0", "tailwind-merge": "^2.6.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "wav-decoder": "^1.3.0", "ws": "^8.18.3", "zod": "^3.25.76", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.25.9", "@babel/preset-typescript": "^7.26.0", "@next/bundle-analyzer": "^15.4.5", "@swc-node/register": "^1.10.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@types/body-parser": "^1.19.5", "@types/connect": "^3.4.38", "@types/cookies": "^0.9.0", "@types/express": "^4.17.21", "@types/express-serve-static-core": "^4.19.1", "@types/fluent-ffmpeg": "^2.1.26", "@types/howler": "^2.2.12", "@types/http-errors": "^2.0.4", "@types/jest": "^29.5.12", "@types/js-cookie": "^3.0.6", "@types/js-levenshtein": "^1.1.3", "@types/keygrip": "^1.0.6", "@types/lodash": "^4.17.13", "@types/matter-js": "^0.19.8", "@types/node": "^20.17.10", "@types/node-fetch": "^2.6.11", "@types/p5": "^1.7.6", "@types/parse-json": "^4.0.2", "@types/react": "^18.3.16", "@types/react-big-calendar": "^1.8.11", "@types/react-color": "^3.0.13", "@types/react-dom": "^18", "@types/react-modal": "^3.16.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "autoprefixer": "^10.0.1", "babel-jest": "^29.7.0", "drizzle-kit": "0.31.4", "eslint": "^8", "eslint-config-next": "14.1.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "node-fetch": "^3.3.2", "npm-run-all": "^4.1.5", "pg": "^8.11.3", "postcss": "^8", "shadcn-ui": "^0.9.4", "tailwindcss": "^3.3.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsx": "^4.7.1", "typescript": "^5.7.2"}}