"use client";

import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import { useExitModal } from "@/store/use-exit-modal";
import { useHeartsModal } from "@/store/use-hearts-modal";
import { usePracticeModal } from "@/store/use-practice-modal";

// Lazy load modals only when they're needed
const ExitModal = dynamic(
  () => import("./exit-modal").then((mod) => ({ default: mod.ExitModal })),
  {
    ssr: false,
    loading: () => null, // No loading state needed for modals
  }
);

const HeartsModal = dynamic(
  () => import("./hearts-modal").then((mod) => ({ default: mod.HeartsModal })),
  {
    ssr: false,
    loading: () => null,
  }
);

const PracticeModal = dynamic(
  () =>
    import("./practice-modal").then((mod) => ({ default: mod.PracticeModal })),
  {
    ssr: false,
    loading: () => null,
  }
);

/**
 * Modal Manager - Handles lazy loading of modals based on their state
 * Only loads modal components when they're actually opened
 * This reduces initial bundle size and improves time to interactive
 *
 * Performance optimization: Once a modal has been opened, we keep it loaded
 * to avoid re-importing on subsequent opens
 */
export const ModalManager = () => {
  const exitModal = useExitModal();
  const heartsModal = useHeartsModal();
  const practiceModal = usePracticeModal();

  // Track which modals have been loaded to avoid re-importing
  const [loadedModals, setLoadedModals] = useState({
    exit: false,
    hearts: false,
    practice: false,
  });

  // Mark modals as loaded when they're first opened
  useEffect(() => {
    if (exitModal.isOpen && !loadedModals.exit) {
      setLoadedModals((prev) => ({ ...prev, exit: true }));
    }
  }, [exitModal.isOpen, loadedModals.exit]);

  useEffect(() => {
    if (heartsModal.isOpen && !loadedModals.hearts) {
      setLoadedModals((prev) => ({ ...prev, hearts: true }));
    }
  }, [heartsModal.isOpen, loadedModals.hearts]);

  useEffect(() => {
    if (practiceModal.isOpen && !loadedModals.practice) {
      setLoadedModals((prev) => ({ ...prev, practice: true }));
    }
  }, [practiceModal.isOpen, loadedModals.practice]);

  return (
    <>
      {/* Only render modals when they're open or have been loaded before */}
      {(exitModal.isOpen || loadedModals.exit) && <ExitModal />}
      {(heartsModal.isOpen || loadedModals.hearts) && <HeartsModal />}
      {(practiceModal.isOpen || loadedModals.practice) && <PracticeModal />}
    </>
  );
};
