// lib/server-auth.ts - Server-side authentication helpers

import { auth } from "@clerk/nextjs/server";
import { db } from "@/db/drizzle";
import { users, teacherStudentRelations } from "@/db/schema";
import { eq } from "drizzle-orm";

export interface ServerUserRecord {
  userId: string;
  email: string;
  role: "teacher" | "student";
  displayName?: string;
  createdAt?: Date;
  schoolUsername?: string | null;
  schoolPassword?: string | null;
}

export interface ServerTeacherStudentRelation {
  studentId: string;
  teacherId: string;
  joinedAt: Date;
}

/**
 * Get user data from database (server-side)
 */
export async function getServerUserData(userId: string): Promise<ServerUserRecord | null> {
  try {
    const userRecords = await db
      .select()
      .from(users)
      .where(eq(users.userId, userId))
      .limit(1);

    if (userRecords.length === 0) {
      return null;
    }

    const user = userRecords[0];
    return {
      userId: user.userId,
      email: user.email,
      role: user.role as "teacher" | "student",
      displayName: user.displayName || undefined,
      createdAt: user.createdAt || undefined,
      schoolUsername: user.schoolUsername,
      schoolPassword: user.schoolPassword,
    };
  } catch (error) {
    console.error("[ServerAuth] Error fetching user data:", error);
    return null;
  }
}

/**
 * Get teacher-student relationship (server-side)
 */
export async function getServerTeacherStudentRelation(
  userId: string
): Promise<ServerTeacherStudentRelation | null> {
  try {
    const relations = await db
      .select()
      .from(teacherStudentRelations)
      .where(eq(teacherStudentRelations.studentId, userId))
      .limit(1);

    if (relations.length === 0) {
      return null;
    }

    const relation = relations[0];
    return {
      studentId: relation.studentId,
      teacherId: relation.teacherId,
      joinedAt: relation.joinedAt,
    };
  } catch (error) {
    console.error("[ServerAuth] Error fetching teacher-student relation:", error);
    return null;
  }
}

/**
 * Check if user is fully onboarded (server-side)
 */
export async function getOnboardingStatus(userId: string): Promise<boolean> {
  try {
    const userData = await getServerUserData(userId);
    
    if (!userData) {
      return false; // User doesn't exist
    }

    // Check teacher onboarding
    if (userData.role === "teacher") {
      return !!(userData.schoolUsername && userData.schoolPassword);
    }

    // Check student onboarding
    if (userData.role === "student") {
      const relation = await getServerTeacherStudentRelation(userId);
      return !!relation;
    }

    return false;
  } catch (error) {
    console.error("[ServerAuth] Error checking onboarding status:", error);
    return false;
  }
}

/**
 * Server-side auth check with onboarding validation
 */
export async function requireAuth(): Promise<{
  userId: string;
  userData: ServerUserRecord | null;
  isOnboarded: boolean;
}> {
  const { userId } = await auth();
  
  if (!userId) {
    throw new Error("Unauthorized");
  }

  const userData = await getServerUserData(userId);
  const isOnboarded = userData ? await getOnboardingStatus(userId) : false;

  return {
    userId,
    userData,
    isOnboarded,
  };
}
