"use client";

import React, {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperative<PERSON><PERSON>le,
  useCallback,
} from "react";
import { getSocket } from "../../../lib/socketClient";
import type { Socket } from "socket.io-client";

// (A) CanvasProps interface
interface CanvasProps {
  canDraw?: boolean;
  userId?: string;
  color: string;
  lineWidth: number;
  tool: "pen" | "eraser";
  onClearCanvas?: () => void;
  opacity?: number;
  // 1.2 Expose onDrawingComplete prop
  onDrawingComplete?: (strokes: StrokeObject[]) => void;
  // This prop is passed from UstadTutor but not used internally by Draw.tsx for a "Done" button
  // It's kept here as per the "Don't delete any lines of code" instruction, though not strictly required for Draw's internal logic.
  onDone?: () => void;
}

// (NEW) Stroke object interface
interface StrokeObject {
  userId: string;
  tool: "pen" | "eraser";
  color: string;
  lineWidth: number;
  opacity: number;
  points: { x: number; y: number }[];
}

// NEW: Interface for the imperative handle methods
export interface CanvasComponentRef {
  clearCanvas: () => void;
  triggerDrawingCompleteNow: () => void; // New method
}

const CanvasComponent = forwardRef<CanvasComponentRef, CanvasProps>( // Updated forwardRef type
  (
    {
      canDraw = true,
      userId,
      color,
      lineWidth,
      tool,
      onClearCanvas,
      opacity = 1.0,
      // 1.2 Expose onDrawingComplete prop
      onDrawingComplete,
      onDone, // Acknowledging the prop, though not used internally by this component
    },
    ref
  ) => {
    const canvasRef = useRef<HTMLCanvasElement | null>(null);
    const svgRef = useRef<SVGSVGElement | null>(null);

    const [isDrawing, setIsDrawing] = useState(false);
    const [lastPoint, setLastPoint] = useState<{ x: number; y: number } | null>(
      null
    );
    const [allStrokes, setAllStrokes] = useState<StrokeObject[]>([]);
    const [currentStroke, setCurrentStroke] = useState<StrokeObject | null>(
      null
    );

    // NEW: Refs to hold the latest state values for imperative methods to read
    const latestAllStrokesRef = useRef<StrokeObject[]>([]);
    const latestCurrentStrokeRef = useRef<StrokeObject | null>(null);
    const latestOnDrawingCompleteRef = useRef<
      ((strokes: StrokeObject[]) => void) | undefined
    >(onDrawingComplete);

    // Ref to track the initial component mount to prevent useEffect from firing prematurely.
    const isInitialMount = useRef(true);

    const [socket, setSocket] = useState<Socket | null>(null);

    // Initialize socket
    useEffect(() => {
      const initSocket = async () => {
        const socketInstance = await getSocket();
        setSocket(socketInstance);
      };
      initSocket();
    }, []);

    // NEW: Effects to keep the refs updated with the latest state and prop values
    useEffect(() => {
      latestAllStrokesRef.current = allStrokes;
    }, [allStrokes]);

    useEffect(() => {
      latestCurrentStrokeRef.current = currentStroke;
    }, [currentStroke]);

    useEffect(() => {
      latestOnDrawingCompleteRef.current = onDrawingComplete;
    }, [onDrawingComplete]);

    // NEW: This effect safely calls the onDrawingComplete callback after the state has updated.
    useEffect(() => {
      // Skip the initial render to prevent firing on mount with an empty array.
      if (isInitialMount.current) {
        isInitialMount.current = false;
        return;
      }
      // On subsequent changes to `allStrokes` (add, erase, clear), notify the parent.
      if (latestOnDrawingCompleteRef.current) {
        latestOnDrawingCompleteRef.current(allStrokes);
      }
    }, [allStrokes]);

    // ----------------------------------------
    // 1) Socket Listeners
    // ----------------------------------------
    useEffect(() => {
      const c = canvasRef.current;
      if (!c) return;
      const ctx = c.getContext("2d");
      if (!ctx) return;

      const drawLineSegment = (
        context: CanvasRenderingContext2D,
        x1: number,
        y1: number,
        x2: number,
        y2: number,
        strokeColor: string,
        lineW: number,
        erase: boolean,
        alpha: number
      ) => {
        context.save();
        context.beginPath();
        context.strokeStyle = erase ? "#ffffff" : strokeColor;
        context.lineWidth = erase ? lineW * 2 : lineW;
        context.globalAlpha = erase ? 1.0 : alpha;
        context.lineCap = "round";
        context.lineJoin = "round";
        context.moveTo(x1, y1);
        context.lineTo(x2, y2);
        context.stroke();
        context.closePath();
        context.restore();
      };

      const handleAnnotationUpdate = (data: {
        userId: string;
        stroke: { x: number; y: number }[];
        color: string;
        lineWidth: number;
        tool: string;
        opacity?: number;
      }) => {
        if (!canvasRef.current) return;
        if (!ctx) return;

        const cWidth = c.width;
        const cHeight = c.height;
        const strokeArr = data.stroke;
        if (strokeArr.length < 2) return;

        const useAlpha = data.tool === "eraser" ? 1.0 : data.opacity ?? 1.0;

        for (let i = 0; i < strokeArr.length - 1; i++) {
          const p1 = strokeArr[i];
          const p2 = strokeArr[i + 1];
          const x1 = p1.x * cWidth;
          const y1 = p1.y * cHeight;
          const x2 = p2.x * cWidth;
          const y2 = p2.y * cHeight;

          drawLineSegment(
            ctx,
            x1,
            y1,
            x2,
            y2,
            data.color,
            data.lineWidth,
            data.tool === "eraser",
            useAlpha
          );
        }
      };

      const handleClearAnnotations = () => {
        if (!canvasRef.current) return;
        ctx.clearRect(0, 0, c.width, c.height);
        setAllStrokes([]);
      };

      if (socket) {
        socket.on("annotation-update", handleAnnotationUpdate);
        socket.on("clear-annotations", handleClearAnnotations);
      }

      return () => {
        if (socket) {
          socket.off("annotation-update", handleAnnotationUpdate);
          socket.off("clear-annotations", handleClearAnnotations);
        }
      };
    }, [socket]);

    // ----------------------------------------
    // 2) Initial Canvas Clear
    // ----------------------------------------
    useEffect(() => {
      if (!canvasRef.current) return;
      const c = canvasRef.current;
      const ctx = c.getContext("2d");
      if (!ctx) return;
      ctx.clearRect(0, 0, c.width, c.height);
    }, []);

    // ----------------------------------------
    // 3) Redraw All Strokes (useCallback)
    // ----------------------------------------
    const redrawAllStrokes = useCallback(() => {
      const c = canvasRef.current;
      if (!c) return;
      const ctx = c.getContext("2d");
      if (!ctx) return;

      ctx.clearRect(0, 0, c.width, c.height);

      // Use the latest allStrokes from ref for redrawing logic
      latestAllStrokesRef.current.forEach((strokeObj) => {
        const useAlpha =
          strokeObj.tool === "eraser" ? 1.0 : strokeObj.opacity ?? 1.0;

        ctx.save();
        ctx.beginPath();
        ctx.strokeStyle =
          strokeObj.tool === "eraser" ? "#ffffff" : strokeObj.color;
        ctx.lineWidth =
          strokeObj.tool === "eraser"
            ? strokeObj.lineWidth * 2
            : strokeObj.lineWidth;
        ctx.globalAlpha = useAlpha;
        ctx.lineCap = "round";
        ctx.lineJoin = "round";

        let firstPoint = true;
        strokeObj.points.forEach((p) => {
          const x = p.x * c.width;
          const y = p.y * c.height;
          if (firstPoint) {
            ctx.moveTo(x, y);
            firstPoint = false;
          } else {
            ctx.lineTo(x, y);
          }
        });

        ctx.stroke();
        ctx.closePath();
        ctx.restore();
      });
    }, []); // ENHANCEMENT: Empty dependency array as it only uses refs, making it stable.

    // ----------------------------------------
    // 4) ResizeObserver
    // ----------------------------------------
    useEffect(() => {
      const c = canvasRef.current;
      if (!c) return;
      const parent = c.parentElement;
      if (!parent) return;

      const handleResize = () => {
        c.width = parent.clientWidth;
        c.height = parent.clientHeight;
        const ctx = c.getContext("2d");
        if (ctx) {
          ctx.clearRect(0, 0, c.width, c.height);
        }
        // Redraw after resize
        redrawAllStrokes();
      };

      const ro = new ResizeObserver(() => {
        handleResize();
      });
      ro.observe(parent);

      // Initial sync
      handleResize();

      return () => ro.disconnect();
    }, [redrawAllStrokes]);

    // ----------------------------------------
    // 5) Pointer Handlers - Enhanced with canDraw checks
    // ----------------------------------------
    const transformPoint = (e: React.PointerEvent<HTMLCanvasElement>) => {
      if (
        e.nativeEvent.offsetX !== undefined &&
        e.nativeEvent.offsetY !== undefined
      ) {
        return { x: e.nativeEvent.offsetX, y: e.nativeEvent.offsetY };
      }
      const canvasElem = e.currentTarget;
      const rect = canvasElem.getBoundingClientRect();
      const scaleX = canvasElem.width / rect.width;
      const scaleY = canvasElem.height / rect.height;
      return {
        x: (e.clientX - rect.left) * scaleX,
        y: (e.clientY - rect.top) * scaleY,
      };
    };

    const handlePointerDown = (e: React.PointerEvent<HTMLCanvasElement>) => {
      // Early return if drawing is not allowed
      if (!canDraw) return;

      setIsDrawing(true);
      const { x, y } = transformPoint(e);
      setLastPoint({ x, y });

      const realUserId =
        userId && userId.trim() !== "" ? userId.trim() : "student_demo_user";
      const newStroke: StrokeObject = {
        userId: realUserId,
        tool,
        color,
        lineWidth,
        opacity,
        points: [
          {
            x: x / (canvasRef.current?.width || 1),
            y: y / (canvasRef.current?.height || 1),
          },
        ],
      };
      setCurrentStroke(newStroke);
    };

    const handlePointerMove = (e: React.PointerEvent<HTMLCanvasElement>) => {
      // Early return if drawing is not allowed
      if (!canDraw) return;
      if (!isDrawing || !canvasRef.current || !lastPoint) return;

      const c = canvasRef.current;
      const ctx = c.getContext("2d");
      if (!ctx) return;

      const { x: currentX, y: currentY } = transformPoint(e);

      if (tool === "eraser") {
        // Eraser logic
        const cWidth = c.width;
        const cHeight = c.height;
        const pointerNorm = {
          x: currentX / cWidth,
          y: currentY / cHeight,
        };
        const threshold = 0.02; // Adjust as needed
        setAllStrokes((prevStrokes) => {
          const newStrokes: StrokeObject[] = [];
          prevStrokes.forEach((stroke) => {
            let segments: { x: number; y: number }[][] = [];
            let currentSegment: { x: number; y: number }[] = [];
            stroke.points.forEach((p) => {
              const dx = p.x - pointerNorm.x;
              const dy = p.y - pointerNorm.y;
              const distance = Math.sqrt(dx * dx + dy * dy);
              if (distance > threshold) {
                currentSegment.push(p);
              } else {
                if (currentSegment.length >= 2) {
                  segments.push(currentSegment);
                }
                currentSegment = [];
              }
            });
            if (currentSegment.length >= 2) {
              segments.push(currentSegment);
            }
            segments.forEach((seg) => {
              newStrokes.push({
                ...stroke,
                points: seg,
              });
            });
          });
          return newStrokes;
        });
        setTimeout(() => {
          redrawAllStrokes();
        }, 0);
      } else {
        // Pen logic
        ctx.beginPath();
        ctx.moveTo(lastPoint.x, lastPoint.y);
        ctx.lineTo(currentX, currentY);
        ctx.globalAlpha = opacity;
        ctx.strokeStyle = color;
        ctx.lineWidth = lineWidth;
        ctx.lineCap = "round";
        ctx.lineJoin = "round";
        ctx.stroke();
        ctx.closePath();
        ctx.globalAlpha = 1.0;

        setCurrentStroke((prev) => {
          if (prev) {
            return {
              ...prev,
              points: [
                ...prev.points,
                {
                  x: currentX / c.width,
                  y: currentY / c.height,
                },
              ],
            };
          }
          return prev;
        });

        const ratioXLast = lastPoint.x / c.width;
        const ratioYLast = lastPoint.y / c.height;
        const ratioXCurrent = currentX / c.width;
        const ratioYCurrent = currentY / c.height;

        const realUserId =
          userId && userId.trim() !== "" ? userId.trim() : "student_demo_user";

        socket?.emit("annotation-update", {
          userId: realUserId,
          stroke: [
            { x: ratioXLast, y: ratioYLast },
            { x: ratioXCurrent, y: ratioYCurrent },
          ],
          color,
          lineWidth,
          tool,
          opacity,
        });
      }

      setLastPoint({ x: currentX, y: currentY });
    };

    const handlePointerUp = () => {
      // Early return if not drawing
      if (!canDraw || !isDrawing) return;

      setIsDrawing(false);
      setLastPoint(null);

      if (currentStroke && currentStroke.points.length > 1) {
        setAllStrokes((prevStrokes) => {
          const updatedStrokes = [...prevStrokes, currentStroke];
          // The side effect that caused the warning has been removed from here.
          return updatedStrokes;
        });
      }
      setCurrentStroke(null);
    };

    const handlePointerLeave = () => {
      if (!canDraw || !isDrawing) return;
      handlePointerUp();
    };

    // ----------------------------------------
    // 6) Clear Canvas
    // ----------------------------------------
    const clearCanvas = useCallback(() => {
      if (!canvasRef.current) return;
      const c = canvasRef.current;
      const ctx = c.getContext("2d");
      if (!ctx) return;

      ctx.clearRect(0, 0, c.width, c.height);
      setAllStrokes([]);

      if (canDraw && userId) {
        const realUserId =
          userId && userId.trim() !== "" ? userId.trim() : "student_demo_user";
        socket?.emit("clear-annotations", { all: false, userId: realUserId });
      }

      onClearCanvas?.();
    }, [canDraw, userId, socket, onClearCanvas]);

    // ----------------------------------------
    // 7) Expose clearCanvas & NEW: triggerDrawingCompleteNow via ref
    // ----------------------------------------
    const triggerDrawingCompleteNow = useCallback(() => {
      // Logic to flush the current stroke remains, but no timeout to clear.
      const currentStrokeToFlush = latestCurrentStrokeRef.current;
      let finalStrokes = latestAllStrokesRef.current;

      // 2. Flush the current stroke into its allStrokes state.
      if (currentStrokeToFlush && currentStrokeToFlush.points.length > 1) {
        const updatedStrokes = [...finalStrokes, currentStrokeToFlush];
        setAllStrokes(updatedStrokes);
        finalStrokes = updatedStrokes; // Use the most up-to-date strokes for the callback

        // Reset drawing state as we've programmatically finished the stroke
        setCurrentStroke(null);
        setIsDrawing(false);
        setLastPoint(null);
      } else {
        // 3. Immediately call onDrawingComplete(allStrokes) even if there's no current stroke.
        latestOnDrawingCompleteRef.current?.(finalStrokes);
      }
    }, []); // This function is stable as it uses refs and state setters.

    useImperativeHandle(
      ref,
      () => ({
        clearCanvas,
        triggerDrawingCompleteNow, // Expose the new method
      }),
      [clearCanvas, triggerDrawingCompleteNow] // Add dependencies
    );

    // ----------------------------------------
    // 8) Render - Enhanced with proper pointer event handling
    // ----------------------------------------
    return (
      <svg
        ref={svgRef}
        className="absolute inset-0 w-full h-full"
        width="100%"
        height="100%"
      >
        <foreignObject x="0" y="0" width="100%" height="100%">
          <canvas
            ref={canvasRef}
            className="absolute inset-0 w-full h-full"
            onPointerDown={handlePointerDown}
            onPointerMove={handlePointerMove}
            onPointerUp={handlePointerUp}
            onPointerLeave={handlePointerLeave}
            style={{
              cursor: canDraw
                ? tool === "eraser"
                  ? "crosshair"
                  : "default"
                : "default",
              // Disable pointer events when drawing is not allowed
              pointerEvents: canDraw ? "auto" : "none",
            }}
          />
        </foreignObject>
      </svg>
    );
  }
);

// 1) Provide a display name for the forwardRef component
CanvasComponent.displayName = "CanvasComponent";

export default CanvasComponent;
