// app/api/test-db/route.ts
import { NextResponse } from "next/server";

export async function GET() {
  try {
    console.log('=== DATABASE TEST API ===');
    console.log('DATABASE_URL exists:', !!process.env.DATABASE_URL);
    console.log('DATABASE_URL preview:', process.env.DATABASE_URL ? process.env.DATABASE_URL.substring(0, 30) + '...' : 'undefined');
    
    if (!process.env.DATABASE_URL) {
      return NextResponse.json({
        error: "DATABASE_URL not found",
        env_keys: Object.keys(process.env).filter(key => key.includes('DATABASE')),
        all_env_keys: Object.keys(process.env).length
      }, { status: 500 });
    }

    // Try to import and test the database
    const db = await import("@/db/drizzle");
    console.log('Database module imported successfully');
    
    // Try a simple query
    const result = await db.default.execute('SELECT 1 as test');
    console.log('Database query successful:', result);
    
    return NextResponse.json({
      success: true,
      message: "Database connection working",
      test_result: result
    });
    
  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json({
      error: "Database test failed",
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
