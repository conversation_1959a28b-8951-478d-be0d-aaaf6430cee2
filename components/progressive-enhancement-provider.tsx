"use client";

import { useEffect } from "react";
import {
  useInitializeProgressiveEnhancement,
  preloadCriticalResources,
} from "./progressive-enhancement";

interface ProgressiveEnhancementProviderProps {
  children: React.ReactNode;
}

/**
 * Progressive Enhancement Provider
 *
 * Initializes progressive enhancement features and resource preloading
 * This should wrap the entire application to enable progressive loading
 */
export const ProgressiveEnhancementProvider = ({
  children,
}: ProgressiveEnhancementProviderProps) => {
  // Initialize progressive enhancement
  useInitializeProgressiveEnhancement();

  return <>{children}</>;
};
